"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button, Input, Card, message, Tabs, Table, Switch, Modal, Form, Select, Space } from "antd";
import { PlusOutlined, EditOutlined, DeleteOutlined } from "@ant-design/icons";

const { TabPane } = Tabs;
const { TextArea } = Input;

interface Provider {
  id: string;
  name: string;
  providerType: string;
  baseUrl: string;
  apiKey: string;
  apiVersion?: string;
  enabled: boolean;
  sortOrder: number;
}

interface SystemConfig {
  configKey: string;
  configValue: string;
  configType: string;
  description?: string;
  isPublic: boolean;
}

interface User {
  id: number;
  username: string;
  email?: string;
  role: string;
  status: string;
  apiQuota: number;
  usedQuota: number;
  createdAt: string;
}

interface ApiLog {
  id: number;
  userId?: number;
  providerId: string;
  modelName: string;
  status: string;
  createdAt: string;
  user?: { username: string };
  provider?: { name: string };
}

export default function AdminPage() {
  const router = useRouter();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [providers, setProviders] = useState<Provider[]>([]);
  const [configs, setConfigs] = useState<SystemConfig[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [logs, setLogs] = useState<ApiLog[]>([]);
  const [stats, setStats] = useState<any>({});
  const [isProviderModalVisible, setIsProviderModalVisible] = useState(false);
  const [isUserModalVisible, setIsUserModalVisible] = useState(false);
  const [editingProvider, setEditingProvider] = useState<Provider | null>(null);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [form] = Form.useForm();
  const [userForm] = Form.useForm();

  // 检查是否已登录
  useEffect(() => {
    const token = localStorage.getItem("admin_token");
    if (token) {
      setIsAuthenticated(true);
      loadData();
    }
  }, []);

  // 管理员登录
  const handleLogin = async () => {
    if (!password) {
      message.error("请输入密码");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/admin/auth", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (data.success) {
        localStorage.setItem("admin_token", data.token);
        setIsAuthenticated(true);
        message.success("登录成功");
        loadData();
      } else {
        message.error(data.error || "登录失败");
      }
    } catch (error) {
      message.error("登录失败");
    } finally {
      setLoading(false);
    }
  };

  // 加载数据
  const loadData = async () => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    try {
      // 加载服务商数据
      const providersResponse = await fetch("/api/admin/providers", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const providersData = await providersResponse.json();
      if (providersData.providers) {
        setProviders(providersData.providers);
      }

      // 加载系统配置
      const configsResponse = await fetch("/api/admin/config", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const configsData = await configsResponse.json();
      if (configsData.configs) {
        setConfigs(configsData.configs);
      }

      // 加载用户数据
      const usersResponse = await fetch("/api/admin/users", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const usersData = await usersResponse.json();
      if (usersData.users) {
        setUsers(usersData.users);
      }

      // 加载统计数据
      const statsResponse = await fetch("/api/admin/logs", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ action: "stats" }),
      });
      const statsData = await statsResponse.json();
      if (statsData.stats) {
        setStats(statsData.stats);
      }

      // 加载API日志
      const logsResponse = await fetch("/api/admin/logs", {
        headers: { Authorization: `Bearer ${token}` },
      });
      const logsData = await logsResponse.json();
      if (logsData.logs) {
        setLogs(logsData.logs);
      }
    } catch (error) {
      message.error("加载数据失败");
    }
  };

  // 保存服务商
  const handleSaveProvider = async (values: any) => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    try {
      const response = await fetch("/api/admin/providers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...values,
          id: editingProvider?.id,
        }),
      });

      const data = await response.json();
      if (data.success) {
        message.success("保存成功");
        setIsProviderModalVisible(false);
        setEditingProvider(null);
        form.resetFields();
        loadData();
      } else {
        message.error(data.error || "保存失败");
      }
    } catch (error) {
      message.error("保存失败");
    }
  };

  // 删除服务商
  const handleDeleteProvider = async (id: string) => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    try {
      const response = await fetch(`/api/admin/providers?id=${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });

      const data = await response.json();
      if (data.success) {
        message.success("删除成功");
        loadData();
      } else {
        message.error(data.error || "删除失败");
      }
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 更新系统配置
  const handleUpdateConfig = async (configKey: string, configValue: string) => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    const updatedConfigs = configs.map(config =>
      config.configKey === configKey ? { ...config, configValue } : config
    );

    try {
      const response = await fetch("/api/admin/config", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ configs: updatedConfigs }),
      });

      const data = await response.json();
      if (data.success) {
        message.success("配置更新成功");
        setConfigs(updatedConfigs);
      } else {
        message.error(data.error || "更新失败");
      }
    } catch (error) {
      message.error("更新失败");
    }
  };

  // 保存用户
  const handleSaveUser = async (values: any) => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    try {
      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...values,
          id: editingUser?.id,
        }),
      });

      const data = await response.json();
      if (data.success) {
        message.success("保存成功");
        setIsUserModalVisible(false);
        setEditingUser(null);
        userForm.resetFields();
        loadData();
      } else {
        message.error(data.error || "保存失败");
      }
    } catch (error) {
      message.error("保存失败");
    }
  };

  // 删除用户
  const handleDeleteUser = async (id: number) => {
    const token = localStorage.getItem("admin_token");
    if (!token) return;

    try {
      const response = await fetch(`/api/admin/users?id=${id}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });

      const data = await response.json();
      if (data.success) {
        message.success("删除成功");
        loadData();
      } else {
        message.error(data.error || "删除失败");
      }
    } catch (error) {
      message.error("删除失败");
    }
  };

  // 登出
  const handleLogout = () => {
    localStorage.removeItem("admin_token");
    setIsAuthenticated(false);
    setPassword("");
  };

  if (!isAuthenticated) {
    return (
      <div style={{ 
        display: "flex", 
        justifyContent: "center", 
        alignItems: "center", 
        height: "100vh",
        background: "#f0f2f5"
      }}>
        <Card title="管理员登录" style={{ width: 400 }}>
          <Input.Password
            placeholder="请输入管理员密码"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            onPressEnter={handleLogin}
            style={{ marginBottom: 16 }}
          />
          <Button 
            type="primary" 
            block 
            loading={loading}
            onClick={handleLogin}
          >
            登录
          </Button>
        </Card>
      </div>
    );
  }

  const providerColumns = [
    { title: "名称", dataIndex: "name", key: "name" },
    { title: "类型", dataIndex: "providerType", key: "providerType" },
    { title: "Base URL", dataIndex: "baseUrl", key: "baseUrl" },
    { title: "API Key", dataIndex: "apiKey", key: "apiKey" },
    { 
      title: "启用", 
      dataIndex: "enabled", 
      key: "enabled",
      render: (enabled: boolean) => <Switch checked={enabled} disabled />
    },
    {
      title: "操作",
      key: "actions",
      render: (record: Provider) => (
        <Space>
          <Button 
            icon={<EditOutlined />} 
            onClick={() => {
              setEditingProvider(record);
              form.setFieldsValue(record);
              setIsProviderModalVisible(true);
            }}
          />
          <Button 
            icon={<DeleteOutlined />} 
            danger
            onClick={() => {
              Modal.confirm({
                title: "确认删除",
                content: `确定要删除服务商 "${record.name}" 吗？`,
                onOk: () => handleDeleteProvider(record.id),
              });
            }}
          />
        </Space>
      ),
    },
  ];

  const userColumns = [
    { title: "用户名", dataIndex: "username", key: "username" },
    { title: "邮箱", dataIndex: "email", key: "email" },
    { title: "角色", dataIndex: "role", key: "role" },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <span style={{ color: status === "active" ? "green" : "red" }}>
          {status === "active" ? "正常" : "禁用"}
        </span>
      )
    },
    {
      title: "配额",
      dataIndex: "apiQuota",
      key: "apiQuota",
      render: (quota: number) => quota === -1 ? "无限制" : quota
    },
    { title: "已用", dataIndex: "usedQuota", key: "usedQuota" },
    {
      title: "操作",
      key: "actions",
      render: (record: User) => (
        <Space>
          <Button
            icon={<EditOutlined />}
            onClick={() => {
              setEditingUser(record);
              userForm.setFieldsValue(record);
              setIsUserModalVisible(true);
            }}
          />
          <Button
            icon={<DeleteOutlined />}
            danger
            disabled={record.role === "admin"}
            onClick={() => {
              Modal.confirm({
                title: "确认删除",
                content: `确定要删除用户 "${record.username}" 吗？`,
                onOk: () => handleDeleteUser(record.id),
              });
            }}
          />
        </Space>
      ),
    },
  ];

  const logColumns = [
    { title: "用户", dataIndex: ["user", "username"], key: "username" },
    { title: "服务商", dataIndex: ["provider", "name"], key: "provider" },
    { title: "模型", dataIndex: "modelName", key: "modelName" },
    {
      title: "状态",
      dataIndex: "status",
      key: "status",
      render: (status: string) => (
        <span style={{ color: status === "success" ? "green" : "red" }}>
          {status === "success" ? "成功" : "失败"}
        </span>
      )
    },
    {
      title: "时间",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (date: string) => new Date(date).toLocaleString()
    },
  ];

  return (
    <div style={{ padding: 24, background: "#f0f2f5", minHeight: "100vh" }}>
      <div style={{ marginBottom: 16, display: "flex", justifyContent: "space-between" }}>
        <h1>QADChat 管理后台</h1>
        <Button onClick={handleLogout}>登出</Button>
      </div>

      <Tabs defaultActiveKey="dashboard">
        <TabPane tab="仪表板" key="dashboard">
          <div style={{ display: "grid", gridTemplateColumns: "repeat(4, 1fr)", gap: 16, marginBottom: 24 }}>
            <Card>
              <div style={{ textAlign: "center" }}>
                <h3>总调用次数</h3>
                <p style={{ fontSize: 24, fontWeight: "bold", color: "#1890ff" }}>
                  {stats.totalCalls || 0}
                </p>
              </div>
            </Card>
            <Card>
              <div style={{ textAlign: "center" }}>
                <h3>成功率</h3>
                <p style={{ fontSize: 24, fontWeight: "bold", color: "#52c41a" }}>
                  {stats.successRate || 0}%
                </p>
              </div>
            </Card>
            <Card>
              <div style={{ textAlign: "center" }}>
                <h3>活跃用户</h3>
                <p style={{ fontSize: 24, fontWeight: "bold", color: "#722ed1" }}>
                  {stats.activeUsers || 0}
                </p>
              </div>
            </Card>
            <Card>
              <div style={{ textAlign: "center" }}>
                <h3>启用服务商</h3>
                <p style={{ fontSize: 24, fontWeight: "bold", color: "#fa8c16" }}>
                  {stats.activeProviders || 0}
                </p>
              </div>
            </Card>
          </div>

          <Card title="最近API调用日志">
            <Table
              dataSource={logs.slice(0, 10)}
              columns={logColumns}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </Card>
        </TabPane>

        <TabPane tab="服务商管理" key="providers">
          <Card 
            title="服务商列表"
            extra={
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingProvider(null);
                  form.resetFields();
                  setIsProviderModalVisible(true);
                }}
              >
                添加服务商
              </Button>
            }
          >
            <Table 
              dataSource={providers} 
              columns={providerColumns}
              rowKey="id"
              pagination={false}
            />
          </Card>
        </TabPane>

        <TabPane tab="用户管理" key="users">
          <Card
            title="用户列表"
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingUser(null);
                  userForm.resetFields();
                  setIsUserModalVisible(true);
                }}
              >
                添加用户
              </Button>
            }
          >
            <Table
              dataSource={users}
              columns={userColumns}
              rowKey="id"
              pagination={{ pageSize: 20 }}
            />
          </Card>
        </TabPane>

        <TabPane tab="系统配置" key="config">
          <Card title="系统配置">
            {configs.map(config => (
              <div key={config.configKey} style={{ marginBottom: 16 }}>
                <label style={{ display: "block", marginBottom: 4, fontWeight: "bold" }}>
                  {config.configKey}
                </label>
                <p style={{ margin: "0 0 8px 0", color: "#666", fontSize: "12px" }}>
                  {config.description}
                </p>
                {config.configType === "boolean" ? (
                  <Switch
                    checked={config.configValue === "true"}
                    onChange={(checked) => 
                      handleUpdateConfig(config.configKey, checked.toString())
                    }
                  />
                ) : (
                  <Input
                    value={config.configValue}
                    onChange={(e) => 
                      handleUpdateConfig(config.configKey, e.target.value)
                    }
                  />
                )}
              </div>
            ))}
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        title={editingProvider ? "编辑服务商" : "添加服务商"}
        open={isProviderModalVisible}
        onCancel={() => {
          setIsProviderModalVisible(false);
          setEditingProvider(null);
          form.resetFields();
        }}
        footer={null}
      >
        <Form form={form} onFinish={handleSaveProvider} layout="vertical">
          <Form.Item name="name" label="名称" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="providerType" label="类型" rules={[{ required: true }]}>
            <Select>
              <Select.Option value="openai">OpenAI</Select.Option>
              <Select.Option value="anthropic">Anthropic</Select.Option>
              <Select.Option value="google">Google</Select.Option>
              <Select.Option value="azure">Azure</Select.Option>
              <Select.Option value="bytedance">ByteDance</Select.Option>
              <Select.Option value="alibaba">Alibaba</Select.Option>
              <Select.Option value="moonshot">Moonshot</Select.Option>
              <Select.Option value="deepseek">DeepSeek</Select.Option>
              <Select.Option value="xai">XAI</Select.Option>
              <Select.Option value="siliconflow">SiliconFlow</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="baseUrl" label="Base URL" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="apiKey" label="API Key" rules={[{ required: true }]}>
            <Input.Password />
          </Form.Item>
          <Form.Item name="apiVersion" label="API Version">
            <Input />
          </Form.Item>
          <Form.Item name="enabled" label="启用" valuePropName="checked">
            <Switch />
          </Form.Item>
          <Form.Item name="sortOrder" label="排序">
            <Input type="number" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setIsProviderModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title={editingUser ? "编辑用户" : "添加用户"}
        open={isUserModalVisible}
        onCancel={() => {
          setIsUserModalVisible(false);
          setEditingUser(null);
          userForm.resetFields();
        }}
        footer={null}
      >
        <Form form={userForm} onFinish={handleSaveUser} layout="vertical">
          <Form.Item name="username" label="用户名" rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name="email" label="邮箱">
            <Input type="email" />
          </Form.Item>
          <Form.Item name="password" label="密码" rules={editingUser ? [] : [{ required: true }]}>
            <Input.Password placeholder={editingUser ? "留空则不修改密码" : "请输入密码"} />
          </Form.Item>
          <Form.Item name="role" label="角色" rules={[{ required: true }]}>
            <Select>
              <Select.Option value="user">普通用户</Select.Option>
              <Select.Option value="admin">管理员</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="status" label="状态" rules={[{ required: true }]}>
            <Select>
              <Select.Option value="active">正常</Select.Option>
              <Select.Option value="disabled">禁用</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item name="apiQuota" label="API配额" rules={[{ required: true }]}>
            <Input type="number" placeholder="-1表示无限制" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setIsUserModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
}
