import { NextRequest, NextResponse } from "next/server";
import { getProviderByType, getProviderById, logApiCall } from "@/app/lib/provider-service";
import { verifyUserToken } from "@/app/lib/user-auth";
import { ProviderType } from "@/app/generated/prisma";
import prisma from "@/app/lib/prisma";

function getIP(req: NextRequest) {
  let ip = req.ip ?? req.headers.get("x-real-ip");
  const forwardedFor = req.headers.get("x-forwarded-for");

  if (!ip && forwardedFor) {
    ip = forwardedFor.split(",").at(0) ?? "";
  }

  return ip;
}

async function forwardToProvider(
  req: NextRequest,
  providerConfig: any,
  path: string = "v1/chat/completions"
) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 60000); // 60秒超时

  try {
    const fetchUrl = `${providerConfig.baseUrl}/${path}`;
    
    // 构建请求头
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    // 根据服务商类型设置认证头
    switch (providerConfig.type) {
      case 'openai':
      case 'azure':
        headers["Authorization"] = `Bearer ${providerConfig.apiKey}`;
        if (providerConfig.type === 'azure' && providerConfig.apiVersion) {
          headers["api-version"] = providerConfig.apiVersion;
        }
        break;
      case 'anthropic':
        headers["x-api-key"] = providerConfig.apiKey;
        headers["anthropic-version"] = providerConfig.apiVersion || "2023-06-01";
        break;
      case 'google':
        headers["x-goog-api-key"] = providerConfig.apiKey;
        break;
      default:
        headers["Authorization"] = `Bearer ${providerConfig.apiKey}`;
    }

    const fetchOptions: RequestInit = {
      headers,
      method: req.method,
      body: req.body,
      redirect: "manual",
      // @ts-ignore
      duplex: "half",
      signal: controller.signal,
    };

    const res = await fetch(fetchUrl, fetchOptions);

    // 处理响应头
    const newHeaders = new Headers(res.headers);
    newHeaders.delete("www-authenticate");
    newHeaders.set("X-Accel-Buffering", "no");

    return new Response(res.body, {
      status: res.status,
      statusText: res.statusText,
      headers: newHeaders,
    });
  } finally {
    clearTimeout(timeoutId);
  }
}

export async function POST(req: NextRequest) {
  console.log("[Chat API] 🚀 POST request received");
  console.log("[Chat API] 📍 URL:", req.url);
  console.log("[Chat API] 📋 Method:", req.method);

  const ip = getIP(req);
  const userAgent = req.headers.get("user-agent") || "";
  let currentUser = null;

  console.log("[Chat API] 🌐 IP:", ip);
  console.log("[Chat API] 🔧 User-Agent:", userAgent);

  try {
    // 验证用户认证（可选，如果需要用户登录才能使用）
    const tokenData = verifyUserToken(req);

    if (tokenData) {
      // 获取用户信息并检查配额
      currentUser = await prisma.user.findUnique({
        where: { id: tokenData.userId },
      });

      if (currentUser) {
        // 检查用户状态
        if (currentUser.status !== "active") {
          return NextResponse.json(
            { error: "Account is disabled" },
            { status: 401 }
          );
        }

        // 检查API配额（如果设置了限制）
        if (currentUser.apiQuota > 0 && currentUser.usedQuota >= currentUser.apiQuota) {
          return NextResponse.json(
            { error: "API quota exceeded" },
            { status: 429 }
          );
        }
      }
    }

    // 解析请求体以获取模型信息
    const body = await req.json();
    const model = body.model;

    if (!model) {
      return NextResponse.json(
        { error: "Model is required" },
        { status: 400 }
      );
    }

    // 从请求头获取服务商信息
    const providerType = req.headers.get("x-provider-type") as ProviderType;
    const providerId = req.headers.get("x-provider-id");

    let providerConfig;
    
    if (providerId) {
      // 使用指定的服务商ID
      providerConfig = await getProviderById(providerId);
    } else if (providerType) {
      // 使用服务商类型
      providerConfig = await getProviderByType(providerType);
    } else {
      // 默认使用OpenAI
      providerConfig = await getProviderByType('openai');
    }

    if (!providerConfig) {
      await logApiCall({
        userId: currentUser?.id,
        providerId: undefined, // 不传递无效的 providerId
        modelName: model,
        status: 'error',
        errorMessage: 'Provider not found or disabled',
        ipAddress: ip || undefined,
        userAgent,
      });

      return NextResponse.json(
        { error: "Provider not found or disabled" },
        { status: 404 }
      );
    }

    // 重新构建请求体
    const modifiedBody = JSON.stringify(body);

    // 过滤请求头，只保留必要的头部，避免非ASCII字符问题
    const cleanHeaders = new Headers();
    cleanHeaders.set("Content-Type", "application/json");
    cleanHeaders.set("Accept", "application/json");

    // 添加服务商类型信息
    if (providerType) {
      cleanHeaders.set("x-provider-type", providerType);
    }
    if (providerId) {
      cleanHeaders.set("x-provider-id", providerId);
    }

    const modifiedReq = new NextRequest(req.url, {
      method: req.method,
      headers: cleanHeaders,
      body: modifiedBody,
    });

    // 转发请求
    const response = await forwardToProvider(modifiedReq, providerConfig);

    // 记录成功的API调用
    await logApiCall({
      userId: currentUser?.id,
      providerId: providerConfig.id,
      modelName: model,
      status: 'success',
      ipAddress: ip || undefined,
      userAgent,
    });

    // 更新用户配额（如果是已登录用户）
    if (currentUser && currentUser.apiQuota > 0) {
      await prisma.user.update({
        where: { id: currentUser.id },
        data: { usedQuota: { increment: 1 } },
      });
    }

    return response;
  } catch (error) {
    console.error("[Chat API] Error:", error);
    
    // 记录失败的API调用
    await logApiCall({
      userId: currentUser?.id,
      providerId: undefined,
      modelName: 'unknown',
      status: 'error',
      errorMessage: error instanceof Error ? error.message : 'Unknown error',
      ipAddress: ip || undefined,
      userAgent,
    });

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return NextResponse.json({ body: "OK" }, { status: 200 });
}
