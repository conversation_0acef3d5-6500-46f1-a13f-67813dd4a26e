import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";
import prisma from "@/app/lib/prisma";
import { encrypt, decrypt } from "@/app/lib/encryption";

// 获取所有服务商
export async function GET(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const providers = await prisma.provider.findMany({
      orderBy: { sortOrder: 'asc' },
    });

    // 解密API Key用于显示（仅显示前几位）
    const safeProviders = providers.map(provider => ({
      ...provider,
      apiKey: decrypt(provider.apiKey).substring(0, 8) + "...",
      modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
      extraConfig: provider.extraConfig ? JSON.parse(provider.extraConfig) : null,
    }));

    return NextResponse.json({
      success: true,
      providers: safeProviders
    });
  } catch (error) {
    console.error("[Admin Providers GET] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch providers" },
      { status: 500 }
    );
  }
}

// 创建或更新服务商
export async function POST(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = await req.json();
    const {
      id,
      name,
      providerType,
      baseUrl,
      apiKey,
      apiVersion,
      modelsConfig,
      extraConfig,
      enabled,
      sortOrder,
    } = data;

    // 验证必填字段
    if (!name || !providerType || !baseUrl || !apiKey) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    const encryptedApiKey = encrypt(apiKey);
    const modelsConfigStr = modelsConfig ? JSON.stringify(modelsConfig) : null;
    const extraConfigStr = extraConfig ? JSON.stringify(extraConfig) : null;

    let provider;

    if (id) {
      // 更新现有服务商
      provider = await prisma.provider.update({
        where: { id },
        data: {
          name,
          providerType,
          baseUrl,
          apiKey: encryptedApiKey,
          apiVersion,
          modelsConfig: modelsConfigStr,
          extraConfig: extraConfigStr,
          enabled: enabled ?? true,
          sortOrder: sortOrder ?? 0,
        },
      });
    } else {
      // 创建新服务商
      const newId = `${providerType}_${Date.now()}`;
      provider = await prisma.provider.create({
        data: {
          id: newId,
          name,
          providerType,
          baseUrl,
          apiKey: encryptedApiKey,
          apiVersion,
          modelsConfig: modelsConfigStr,
          extraConfig: extraConfigStr,
          enabled: enabled ?? true,
          sortOrder: sortOrder ?? 0,
        },
      });
    }

    return NextResponse.json({
      success: true,
      provider: {
        ...provider,
        apiKey: apiKey.substring(0, 8) + "...", // 返回时隐藏完整API Key
        modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
        extraConfig: provider.extraConfig ? JSON.parse(provider.extraConfig) : null,
      },
    });
  } catch (error) {
    console.error("[Admin Providers POST] Error:", error);
    return NextResponse.json(
      { error: "Failed to save provider" },
      { status: 500 }
    );
  }
}

// 删除服务商
export async function DELETE(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "Provider ID is required" },
        { status: 400 }
      );
    }

    await prisma.provider.delete({
      where: { id },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[Admin Providers DELETE] Error:", error);
    return NextResponse.json(
      { error: "Failed to delete provider" },
      { status: 500 }
    );
  }
}
