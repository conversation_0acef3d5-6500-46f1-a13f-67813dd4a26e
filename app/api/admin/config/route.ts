import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";

// 模拟系统配置数据
let configs = [
  {
    configKey: "max_tokens_per_request",
    configValue: "4000",
    configType: "number",
    description: "每次请求的最大 Token 数量",
  },
  {
    configKey: "enable_registration",
    configValue: "true",
    configType: "boolean",
    description: "是否允许用户注册",
  },
  {
    configKey: "default_user_quota",
    configValue: "1000",
    configType: "number",
    description: "新用户默认配额",
  },
  {
    configKey: "system_announcement",
    configValue: "欢迎使用 QADChat！",
    configType: "string",
    description: "系统公告",
  },
];

// 获取所有系统配置
export async function GET(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    return NextResponse.json({
      success: true,
      configs
    });
  } catch (error) {
    console.error("[Admin Config GET] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch configs" },
      { status: 500 }
    );
  }
}

// 更新系统配置
export async function POST(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { configs: newConfigs } = await req.json();

    if (!Array.isArray(newConfigs)) {
      return NextResponse.json(
        { error: "Configs must be an array" },
        { status: 400 }
      );
    }

    // 批量更新配置
    newConfigs.forEach((newConfig: any) => {
      const index = configs.findIndex(c => c.configKey === newConfig.configKey);
      if (index !== -1) {
        configs[index] = { ...configs[index], ...newConfig };
      } else {
        configs.push(newConfig);
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[Admin Config POST] Error:", error);
    return NextResponse.json(
      { error: "Failed to update configs" },
      { status: 500 }
    );
  }
}

// 更新单个配置项
export async function PUT(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { key, value } = await req.json();

    if (!key) {
      return NextResponse.json(
        { error: "Config key is required" },
        { status: 400 }
      );
    }

    const index = configs.findIndex(c => c.configKey === key);
    if (index !== -1) {
      configs[index].configValue = value.toString();
    }

    return NextResponse.json({
      success: true,
      config: configs[index],
    });
  } catch (error) {
    console.error("[Admin Config PUT] Error:", error);
    return NextResponse.json(
      { error: "Failed to update config" },
      { status: 500 }
    );
  }
}

// 删除配置项
export async function DELETE(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const configKey = searchParams.get("key");

    if (!configKey) {
      return NextResponse.json(
        { error: "Config key is required" },
        { status: 400 }
      );
    }

    const index = configs.findIndex(c => c.configKey === configKey);
    if (index !== -1) {
      configs.splice(index, 1);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[Admin Config DELETE] Error:", error);
    return NextResponse.json(
      { error: "Failed to delete config" },
      { status: 500 }
    );
  }
}
