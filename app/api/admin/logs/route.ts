import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";

// 模拟日志数据
const generateMockLogs = (count: number) => {
  const logs = [];
  const models = ["gpt-4", "gpt-3.5-turbo", "claude-3", "gemini-pro"];
  const providers = ["openai_1", "anthropic_1", "google_1"];
  const statuses = ["success", "error", "pending"];

  for (let i = 0; i < count; i++) {
    logs.push({
      id: i + 1,
      modelName: models[Math.floor(Math.random() * models.length)],
      providerId: providers[Math.floor(Math.random() * providers.length)],
      status: statuses[Math.floor(Math.random() * statuses.length)],
      totalTokens: Math.floor(Math.random() * 2000) + 100,
      createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
    });
  }

  return logs.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
};

const mockLogs = generateMockLogs(50);

// 获取API调用日志
export async function GET(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "20");
    const skip = (page - 1) * limit;

    // 分页处理
    const paginatedLogs = mockLogs.slice(skip, skip + limit);
    const total = mockLogs.length;

    return NextResponse.json({
      success: true,
      logs: paginatedLogs,
      total,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error("[Admin Logs GET] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch logs" },
      { status: 500 }
    );
  }
}


