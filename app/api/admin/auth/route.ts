import { NextRequest, NextResponse } from "next/server";
import { getSystemConfig } from "@/app/lib/provider-service";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-jwt-secret-key";

export async function POST(req: NextRequest) {
  try {
    const { password } = await req.json();

    if (!password) {
      return NextResponse.json(
        { error: "Password is required" },
        { status: 400 }
      );
    }

    // 获取管理员密码
    const adminPassword = await getSystemConfig("admin_password");
    
    if (!adminPassword) {
      return NextResponse.json(
        { error: "Admin password not configured" },
        { status: 500 }
      );
    }

    // 验证密码（直接比较明文密码）
    const isValid = password === adminPassword;

    if (!isValid) {
      return NextResponse.json(
        { error: "Invalid password" },
        { status: 401 }
      );
    }

    // 生成 JWT token
    const token = jwt.sign(
      { role: "admin", timestamp: Date.now() },
      JWT_SECRET,
      { expiresIn: "24h" }
    );

    return NextResponse.json({
      success: true,
      token,
      expiresIn: "24h",
    });
  } catch (error) {
    console.error("[Admin Auth] Error:", error);
    return NextResponse.json(
      { error: "Authentication failed" },
      { status: 500 }
    );
  }
}


