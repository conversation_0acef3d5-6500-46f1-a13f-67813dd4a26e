import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";
import prisma from "@/app/lib/prisma";

// 获取系统配置
export async function GET(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // 获取前端配置禁用状态
    const frontendConfigDisabled = await prisma.systemConfig.findUnique({
      where: { configKey: 'disable_frontend_config' },
    });

    // 获取所有启用的服务商
    const providers = await prisma.provider.findMany({
      where: { enabled: true },
      orderBy: { sortOrder: 'asc' },
    });

    return NextResponse.json({
      success: true,
      frontendConfigDisabled: frontendConfigDisabled?.configValue === 'true',
      providers: providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.providerType,
        baseUrl: provider.baseUrl,
        modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
      })),
    });
  } catch (error) {
    console.error("[Admin System Config GET] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch system config" },
      { status: 500 }
    );
  }
}

// 更新系统配置
export async function POST(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { disableFrontendConfig } = await req.json();

    // 更新前端配置禁用状态
    await prisma.systemConfig.upsert({
      where: { configKey: 'disable_frontend_config' },
      update: { configValue: disableFrontendConfig ? 'true' : 'false' },
      create: {
        configKey: 'disable_frontend_config',
        configValue: disableFrontendConfig ? 'true' : 'false',
        configType: 'boolean',
        description: '是否禁用前端配置',
        isPublic: false,
      },
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[Admin System Config POST] Error:", error);
    return NextResponse.json(
      { error: "Failed to update system config" },
      { status: 500 }
    );
  }
}
