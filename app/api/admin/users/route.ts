import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";

// 模拟用户数据存储
let users = [
  {
    id: 1,
    username: "admin",
    email: "<EMAIL>",
    role: "admin",
    status: "active",
    apiQuota: -1,
    usedQuota: 0,
    createdAt: new Date().toISOString(),
  },
  {
    id: 2,
    username: "user1",
    email: "<EMAIL>",
    role: "user",
    status: "active",
    apiQuota: 1000,
    usedQuota: 150,
    createdAt: new Date().toISOString(),
  },
  {
    id: 3,
    username: "user2",
    email: "<EMAIL>",
    role: "user",
    status: "disabled",
    apiQuota: 500,
    usedQuota: 500,
    createdAt: new Date().toISOString(),
  },
];

let nextUserId = 4;

// 获取所有用户
export async function GET(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    return NextResponse.json({
      success: true,
      users,
    });
  } catch (error) {
    console.error("[Admin Users GET] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch users" },
      { status: 500 }
    );
  }
}

// 创建或更新用户
export async function POST(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const data = await req.json();
    const {
      id,
      username,
      email,
      role,
      status,
      apiQuota,
    } = data;

    // 验证必填字段
    if (!username) {
      return NextResponse.json(
        { error: "Username is required" },
        { status: 400 }
      );
    }

    let user;

    if (id) {
      // 更新现有用户
      const index = users.findIndex(u => u.id === parseInt(id));
      if (index !== -1) {
        users[index] = {
          ...users[index],
          username,
          email: email || users[index].email,
          role: role || users[index].role,
          status: status || users[index].status,
          apiQuota: apiQuota ?? users[index].apiQuota,
        };
        user = users[index];
      }
    } else {
      // 创建新用户
      const existingUser = users.find(u => u.username === username);
      if (existingUser) {
        return NextResponse.json(
          { error: "Username already exists" },
          { status: 409 }
        );
      }

      user = {
        id: nextUserId++,
        username,
        email: email || "",
        role: role || "user",
        status: status || "active",
        apiQuota: apiQuota ?? 1000,
        usedQuota: 0,
        createdAt: new Date().toISOString(),
      };
      users.push(user);
    }

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("[Admin Users POST] Error:", error);
    return NextResponse.json(
      { error: "Failed to save user" },
      { status: 500 }
    );
  }
}

// 更新用户状态
export async function PATCH(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");
    const data = await req.json();
    const { status } = data;

    if (!id) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    const userId = parseInt(id);
    const index = users.findIndex(u => u.id === userId);

    if (index !== -1) {
      users[index].status = status;
    }

    return NextResponse.json({
      success: true,
      user: users[index],
    });
  } catch (error) {
    console.error("[Admin Users PATCH] Error:", error);
    return NextResponse.json(
      { error: "Failed to update user" },
      { status: 500 }
    );
  }
}

// 删除用户
export async function DELETE(req: NextRequest) {
  if (!verifyAdminToken(req)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get("id");

    if (!id) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    const userId = parseInt(id);
    const user = users.find(u => u.id === userId);

    // 不允许删除管理员账户
    if (user?.role === "admin") {
      return NextResponse.json(
        { error: "Cannot delete admin user" },
        { status: 403 }
      );
    }

    const index = users.findIndex(u => u.id === userId);
    if (index !== -1) {
      users.splice(index, 1);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("[Admin Users DELETE] Error:", error);
    return NextResponse.json(
      { error: "Failed to delete user" },
      { status: 500 }
    );
  }
}
