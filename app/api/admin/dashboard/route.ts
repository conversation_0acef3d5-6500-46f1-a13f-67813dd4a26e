import { NextRequest, NextResponse } from "next/server";
import { verifyAdminToken } from "@/app/lib/admin-auth";

export async function GET(req: NextRequest) {
  try {
    // 验证用户身份和管理员权限
    if (!verifyAdminToken(req)) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 模拟统计数据 - 在实际项目中这里应该从数据库获取真实数据
    const stats = {
      userCount: 12,
      providerCount: 5,
      todayCalls: 156,
      totalCost: 89.5,
    };

    return NextResponse.json({
      success: true,
      stats,
    });
  } catch (error) {
    console.error("Dashboard API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
