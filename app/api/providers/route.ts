import { NextRequest, NextResponse } from "next/server";
import { getEnabledProviders, isFrontendConfigDisabled } from "@/app/lib/provider-service";

export async function GET(req: NextRequest) {
  try {
    // 检查是否禁用前端配置
    const frontendConfigDisabled = await isFrontendConfigDisabled();
    
    if (frontendConfigDisabled) {
      // 如果禁用前端配置，返回数据库中的服务商列表
      const providers = await getEnabledProviders();
      
      // 不返回敏感信息（API Key）
      const safeProviders = providers.map(provider => ({
        id: provider.id,
        name: provider.name,
        type: provider.type,
        baseUrl: provider.baseUrl,
        enabled: provider.enabled,
        modelsConfig: provider.modelsConfig,
      }));

      return NextResponse.json({
        providers: safeProviders,
        frontendConfigDisabled: true,
      });
    } else {
      // 如果允许前端配置，返回空列表，让前端使用本地配置
      return NextResponse.json({
        providers: [],
        frontendConfigDisabled: false,
      });
    }
  } catch (error) {
    console.error("[Providers API] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch providers" },
      { status: 500 }
    );
  }
}

// 移除 edge runtime，因为需要访问数据库
