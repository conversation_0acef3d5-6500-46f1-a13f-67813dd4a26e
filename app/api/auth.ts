import { NextRequest } from "next/server";
import { ACCESS_CODE_PREFIX, ModelProvider } from "../constant";
import { getProviderByType, isFrontendConfigDisabled } from "../lib/provider-service";
import { ProviderType } from "../generated/prisma";

function getIP(req: NextRequest) {
  let ip = req.ip ?? req.headers.get("x-real-ip");
  const forwardedFor = req.headers.get("x-forwarded-for");

  if (!ip && forwardedFor) {
    ip = forwardedFor.split(",").at(0) ?? "";
  }

  return ip;
}

function parseApiKey(bearToken: string) {
  const token = bearToken.trim().replaceAll("Bearer ", "").trim();
  const isApiKey = !token.startsWith(ACCESS_CODE_PREFIX);

  return {
    accessCode: isApiKey ? "" : token.slice(ACCESS_CODE_PREFIX.length),
    apiKey: isApiKey ? token : "",
  };
}

// 将 ModelProvider 映射到 ProviderType
function mapModelProviderToProviderType(modelProvider: ModelProvider): ProviderType {
  switch (modelProvider) {
    case ModelProvider.GPT:
      return 'openai';
    case ModelProvider.GeminiPro:
      return 'google';
    case ModelProvider.Claude:
      return 'anthropic';
    case ModelProvider.Doubao:
      return 'bytedance';
    case ModelProvider.Qwen:
      return 'alibaba';
    case ModelProvider.Moonshot:
      return 'moonshot';
    case ModelProvider.DeepSeek:
      return 'deepseek';
    case ModelProvider.XAI:
      return 'xai';
    case ModelProvider.SiliconFlow:
      return 'siliconflow';
    default:
      return 'openai';
  }
}

export async function auth(req: NextRequest, modelProvider: ModelProvider) {
  const authToken = req.headers.get("Authorization") ?? "";
  const { accessCode, apiKey } = parseApiKey(authToken);

  console.log("[User IP] ", getIP(req));
  console.log("[Time] ", new Date().toLocaleString());

  try {
    // 检查是否禁用前端配置
    const frontendConfigDisabled = await isFrontendConfigDisabled();

    if (frontendConfigDisabled) {
      // 使用数据库配置，检查服务商是否可用
      const providerType = mapModelProviderToProviderType(modelProvider);
      const provider = await getProviderByType(providerType);

      if (!provider) {
        return {
          error: true,
          msg: `Provider ${providerType} is not configured or disabled`,
        };
      }

      console.log("[Auth] use database provider config");
      return {
        error: false,
        provider,
      };
    } else {
      // 使用前端配置，需要API密钥
      if (!apiKey) {
        return {
          error: true,
          msg: "API key is required",
        };
      }

      console.log("[Auth] use user api key");
      return {
        error: false,
      };
    }
  } catch (error) {
    console.error("[Auth] Error:", error);
    return {
      error: true,
      msg: "Authentication failed",
    };
  }
}
