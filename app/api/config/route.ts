import { NextResponse } from "next/server";
import { getSystemConfig, isFrontendConfigDisabled } from "@/app/lib/provider-service";

// 默认配置
const DEFAULT_CONFIG = {
  needCode: false,
  hideUserApiKey: false,
  disableGPT4: false,
  hideBalanceQuery: false,
  disableFastLink: false,
  customModels: "",
  defaultModel: "",
  visionModels: "",
};

declare global {
  type DangerConfig = typeof DEFAULT_CONFIG;
}

async function handle() {
  try {
    // 检查是否禁用前端配置
    const frontendConfigDisabled = await isFrontendConfigDisabled();

    let defaultModel = "";

    // 只有在禁用前端配置时，才从数据库读取系统默认模型
    if (frontendConfigDisabled) {
      defaultModel = await getSystemConfig("default_model") || "gpt-4o-mini";
    }
    // 如果允许前端配置，则不设置默认模型，让前端使用用户自己的配置

    const config = {
      ...DEFAULT_CONFIG,
      hideUserApiKey: frontendConfigDisabled, // 如果禁用前端配置，则隐藏API Key输入
      defaultModel, // 只有在禁用前端配置时才有值
      frontendConfigDisabled, // 添加这个字段供前端判断
    };

    return NextResponse.json(config);
  } catch (error) {
    console.error("[Config API] Error:", error);
    // 出错时返回默认配置
    return NextResponse.json(DEFAULT_CONFIG);
  }
}

export const GET = handle;
export const POST = handle;
