import { NextRequest, NextResponse } from "next/server";
import { verifyUserToken } from "@/app/lib/user-auth";
import prisma from "@/app/lib/prisma";

export async function GET(req: NextRequest) {
  try {
    const tokenData = verifyUserToken(req);
    if (!tokenData) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: tokenData.userId },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        status: true,
        apiQuota: true,
        usedQuota: true,
        lastLoginAt: true,
        createdAt: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    if (user.status !== "active") {
      return NextResponse.json(
        { error: "Account is disabled" },
        { status: 401 }
      );
    }

    return NextResponse.json({
      success: true,
      user,
    });
  } catch (error) {
    console.error("[User Me] Error:", error);
    return NextResponse.json(
      { error: "Failed to get user info" },
      { status: 500 }
    );
  }
}
