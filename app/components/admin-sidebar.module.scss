.admin-sidebar {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.admin-menu {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.admin-menu-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  border: 2px solid transparent;
  user-select: none;

  &:hover {
    background-color: var(--hover-color);
  }

  &-active {
    background-color: var(--primary);
    color: white;
    border-color: var(--primary);

    .admin-menu-icon {
      transform: scale(1.1);
    }

    .admin-menu-title {
      font-weight: 600;
    }

    &:hover {
      background-color: var(--primary);
    }
  }

  &-narrow {
    justify-content: center;
    padding: 12px 8px;

    .admin-menu-icon {
      font-size: 20px;
    }
  }
}

.admin-menu-icon {
  font-size: 18px;
  margin-right: 12px;
  transition: transform 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
}

.admin-menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.admin-menu-title {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.admin-menu-description {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.2;
}

// 窄侧边栏样式
.admin-menu-item-narrow {
  .admin-menu-icon {
    margin-right: 0;
  }
}
