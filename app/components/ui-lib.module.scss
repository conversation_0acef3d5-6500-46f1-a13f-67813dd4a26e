@import "../styles/animation.scss";

.card {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  padding: 10px;
}

.popover {
  position: relative;
  z-index: 2;
}

.popover-content {
  position: absolute;
  width: 350px;
  animation: slide-in 0.3s ease;
  right: 0;
  top: calc(100% + 10px);
}
@media screen and (max-width: 600px) {
  .popover-content {
    width: auto;
  }
}
.popover-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.list-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 40px;
  border-bottom: var(--border-in-light);
  padding: 10px 20px;
  animation: slide-in ease 0.6s;

  .list-header {
    display: flex;
    align-items: center;

    .list-icon {
      margin-right: 10px;
    }

    .list-item-title {
      font-size: 14px;
      font-weight: bolder;
    }

    .list-item-sub-title {
      font-size: 12px;
      font-weight: normal;
    }
  }

  &.vertical {
    flex-direction: column;
    align-items: start;
    .list-header {
      .list-item-title {
        margin-bottom: 5px;
      }
      .list-item-sub-title {
        margin-bottom: 2px;
      }
    }
  }
}

.list {
  border: var(--border-in-light);
  border-radius: 10px;
  box-shadow: var(--card-shadow);
  margin-bottom: 20px;
  animation: slide-in ease 0.3s;
  background: var(--white);
}

.list .list-item:last-child {
  border: 0;
}

.modal-container {
  box-shadow: var(--card-shadow);
  background-color: var(--white);
  border-radius: 12px;
  width: 80vw;
  max-width: 900px;
  min-width: 300px;
  animation: slide-in ease 0.3s;

  --modal-padding: 20px;

  &-max {
    width: 95vw;
    max-width: unset;
    height: 95vh;
    display: flex;
    flex-direction: column;

    .modal-content {
      max-height: unset !important;
      flex-grow: 1;
    }
  }

  .modal-header {
    padding: var(--modal-padding);
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: var(--border-in-light);

    .modal-title {
      font-weight: bolder;
      font-size: 16px;
    }

    .modal-header-actions {
      display: flex;

      .modal-header-action {
        cursor: pointer;

        &:not(:last-child) {
          margin-right: 20px;
        }

        &:hover {
          filter: brightness(1.2);
        }
      }
    }
  }

  .modal-content {
    max-height: 40vh;
    padding: var(--modal-padding);
    overflow: auto;
  }

  .modal-footer {
    padding: var(--modal-padding);
    display: flex;
    justify-content: flex-end;
    border-top: var(--border-in-light);
    box-shadow: var(--shadow);

    .modal-actions {
      display: flex;
      align-items: center;

      .modal-action {
        &:not(:last-child) {
          margin-right: 20px;
        }
      }
    }
  }
}

@media screen and (max-width: 600px) {
  .modal-container {
    width: 100vw;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;

    .modal-content {
      max-height: 50vh;
    }
  }
}

.show {
  opacity: 1;
  transition: all ease 0.3s;
  transform: translateY(0);
  position: fixed;
  left: 0;
  bottom: 0;
  animation: slide-in ease 0.6s;
  z-index: 99999;
}

.hide {
  opacity: 0;
  transition: all ease 0.3s;
  transform: translateY(20px);
}

.toast-container {
  position: fixed;
  bottom: 5vh;
  left: 0;
  width: 100vw;
  display: flex;
  justify-content: center;
  pointer-events: none;

  .toast-content {
    max-width: 80vw;
    word-break: break-all;
    font-size: 14px;
    background-color: var(--white);
    box-shadow: var(--card-shadow);
    border: var(--border-in-light);
    color: var(--black);
    padding: 10px 20px;
    border-radius: 50px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    pointer-events: all;

    .toast-action {
      padding-left: 20px;
      color: var(--primary);
      opacity: 0.8;
      border: 0;
      background: none;
      cursor: pointer;
      font-family: inherit;

      &:hover {
        opacity: 1;
      }
    }
  }
}

.input {
  border: var(--border-in-light);
  border-radius: 10px;
  padding: 10px;
  font-family: inherit;
  background-color: var(--white);
  color: var(--black);
  resize: none;
  min-width: 50px;
}

.select-with-icon {
  position: relative;
  max-width: fit-content;

  &.left-align-option {
    option {
      text-align: left;
    }
  }

  .select-with-icon-select {
    height: 100%;
    border: var(--border-in-light);
    padding: 10px 35px 10px 10px;
    border-radius: 10px;
    appearance: none;
    cursor: pointer;
    background-color: var(--white);
    color: var(--black);
    text-align: center;
  }

  .select-with-icon-icon {
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    pointer-events: none;
  }
}

.modal-input {
  height: 100%;
  width: 100%;
  border-radius: 10px;
  border: var(--border-in-light);
  box-shadow: 0 -2px 5px rgba(0, 0, 0, 0.03);
  background-color: var(--white);
  color: var(--black);
  font-family: inherit;
  padding: 10px;
  resize: none;
  outline: none;
  box-sizing: border-box;

  &:focus {
    border: 1px solid var(--primary);
  }
}

.selector {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;

  .selector-item-disabled {
    opacity: 0.6;
  }

  .selector-item-selected {
    background-color: var(--hover-color) !important;
  }

  &-content {
    min-width: 300px;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .list {
      max-height: 90vh;
      overflow-x: hidden;
      overflow-y: auto;

      .list-item {
        cursor: pointer;
        background-color: var(--white);

        &:hover {
          filter: brightness(0.95);
        }

        &:active {
          filter: brightness(0.9);
        }
      }
    }
  }

  &-search {
    padding: 16px;
    border-bottom: var(--border-in-light);
    background: var(--white);
    border-radius: 12px 12px 0 0;
  }

  &-search-input {
    width: 100%;
    padding: 8px 12px;
    border: var(--border-in-light);
    border-radius: 8px;
    background: var(--white);
    color: var(--black);
    font-size: 14px;
    outline: none;

    &:focus {
      border-color: var(--primary);
    }

    &::placeholder {
      color: var(--black);
      opacity: 0.5;
    }
  }

  &-groups {
    flex: 1;
    overflow-y: auto;
    background: var(--white);
    border-radius: 0 0 12px 12px;
  }

  &-group {
    &:not(:last-child) {
      border-bottom: var(--border-in-light);
    }
  }

  &-group-header {
    padding: 12px 16px 8px 16px;
    font-size: 12px;
    font-weight: 600;
    color: var(--black);
    opacity: 0.7;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    background: var(--second);
    border-bottom: var(--border-in-light);
  }
}

/* 新的模型选择器模态框样式 */
.model-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fade-in 0.2s ease;
}

.model-selector-modal {
  width: 90vw;
  max-width: 480px;
  max-height: 80vh;
  background: var(--white);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slide-up 0.3s ease;
}

.model-selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: var(--border-in-light);
  background: var(--white);
}

.model-selector-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
}

.model-selector-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: 6px; // 与对话设置modal一致
  cursor: pointer;
  color: var(--black);
  transition: all 0.2s ease;

  &:hover {
    filter: brightness(1.2); // 与对话设置modal一致的悬停效果
    background: var(--hover-color);
  }

  svg {
    width: 16px;
    height: 16px;
    opacity: 0.8;
    transition: all 0.2s ease;
    filter: none !important; // 确保不受全局暗夜模式影响
  }

  &:hover svg {
    opacity: 1;
  }

  // 确保no-dark类生效
  &.no-dark svg {
    filter: none !important;
  }
}

.model-selector-search {
  padding: 16px 24px;
  border-bottom: var(--border-in-light);
  background: var(--white);
  display: block;
  width: 100%;
  box-sizing: border-box;
}

.model-selector-search-input {
  width: 100% !important;
  max-width: none !important;
  padding: 12px 16px;
  border: var(--border-in-light);
  border-radius: 12px;
  background: var(--second);
  color: var(--black);
  font-size: 14px;
  outline: none;
  transition: all 0.2s ease;
  box-sizing: border-box;
  display: block;
  margin: 0;
  text-align: left !important;

  &:focus {
    border-color: var(--primary);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

.model-selector-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.model-selector-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 24px;
  color: var(--black);
  opacity: 0.6;
  font-size: 14px;
}

.model-selector-group {
  &:not(:last-child) {
    margin-bottom: 16px;
  }
}

.model-selector-group-title {
  padding: 12px 24px 8px 24px;
  font-size: 12px;
  font-weight: 600;
  color: var(--black);
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--second);
  margin: 0 16px;
  border-radius: 8px;
}

.model-selector-group-items {
  padding: 8px 16px 0 16px;
}

.model-selector-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-bottom: 4px;

  &:hover {
    background: var(--hover-color);
  }

  &:active {
    transform: scale(0.98);
  }

  &-selected {
    background: var(--primary);
    color: white;

    .model-selector-item-title {
      color: white;
    }

    .model-selector-item-subtitle {
      color: rgba(255, 255, 255, 0.8);
    }

    &:hover {
      background: var(--primary);
      filter: brightness(1.1);
    }
  }

  &-disabled {
    opacity: 0.5;
    cursor: not-allowed;

    &:hover {
      background: transparent;
    }

    &:active {
      transform: none;
    }
  }
}

.model-selector-item-icon {
  width: 32px;
  height: 32px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  // 确保模型图标在暗夜模式下正常显示
  svg {
    filter: none !important;
    opacity: 1;
  }

  // 如果图标有no-dark类，确保它生效
  .no-dark svg {
    filter: none !important;
  }
}

.model-selector-item-info {
  flex: 1;
  min-width: 0;
}

.model-selector-item-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 2px;
  // 确保在暗夜模式下文字清晰可见
  opacity: 1;
}

.model-selector-item-subtitle {
  font-size: 12px;
  color: var(--black);
  opacity: 0.7;
  // 在暗夜模式下提高副标题的可见度
}

.model-selector-item-check {
  width: 20px;
  height: 20px;
  margin-left: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 14px;
    height: 14px;
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 暗夜模式优化 - 参考对话设置modal的简洁处理 */
:global(.dark) {
  .model-selector-close {
    &:hover {
      background: rgba(255, 255, 255, 0.1); // 暗夜模式下的悬停背景
    }
  }
}

/* 简化的暗夜模式处理 */
body.dark {
  .model-selector-close {
    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
}

/* 确保模型选择器中的图标正常显示 */
.model-selector-modal {
  // 覆盖全局的 div:not(.no-dark) > svg 规则
  div > svg {
    filter: none !important;
  }

  .no-dark svg,
  .user-avatar {
    filter: none !important;
  }

  // 专门针对模型图标的样式
  .model-selector-item-icon {
    svg, .user-avatar {
      filter: none !important;
      opacity: 1;
    }
  }
}

/* 多选模型选择器底部操作栏样式 */
.model-selector-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-top: var(--border-in-light);
  background: var(--white);
  gap: 12px;
}

.model-selector-clear,
.model-selector-confirm {
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.model-selector-clear {
  background: var(--hover-color);
  color: var(--black);

  &:hover:not(:disabled) {
    background: var(--border-in-light);
  }
}

.model-selector-confirm {
  background: var(--primary);
  color: white;
  flex: 1;
  max-width: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;

  &:hover:not(:disabled) {
    background: var(--primary);
    filter: brightness(1.1);
  }

  &:disabled {
    background: var(--border-in-light);
    color: var(--black);
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.model-selector-confirm-hint {
  font-size: 11px;
  font-weight: 400;
  opacity: 0.8;
}
