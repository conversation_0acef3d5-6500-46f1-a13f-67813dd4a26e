import React, { useState, useEffect, useMemo } from "react";
import { useAccessStore } from "../store";
import { useUserStore } from "../store/user";
import { ServiceProvider } from "../constant";
import { showConfirm, showToast } from "./ui-lib";
import { IconButton } from "./button";
import { List, ListItem } from "./ui-lib";
import { ModelManager } from "./model-manager";
// import { AddCustomProvider } from "./add-custom-provider";
import { ProviderIcon } from "./provider-icon";
import { ModelCapabilityIcons } from "./model-capability-icons";
import { getModelCapabilitiesWithCustomConfig } from "../config/model-capabilities";
import DownIcon from "../icons/down.svg";
import styles from "./settings.module.scss";
import Locale from "../locales";

// 通用的认证 API 请求函数
const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
  const userStore = useUserStore.getState();
  const token = userStore.token;
  
  if (!token) {
    throw new Error("No authentication token");
  }

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      "Authorization": `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
};

// 添加自定义服务商模态框
interface AddCustomProviderModalProps {
  onClose: () => void;
}

function AddCustomProviderModal({ onClose }: AddCustomProviderModalProps) {
  const accessStore = useAccessStore();
  const [formData, setFormData] = useState({
    name: "",
    type: "openai" as any,
    apiKey: "",
    endpoint: "",
  });

  const handleSubmit = () => {
    if (!formData.name.trim() || !formData.apiKey.trim()) {
      showToast("请填写必填字段");
      return;
    }

    accessStore.addCustomProvider({
      name: formData.name.trim(),
      type: formData.type,
      apiKey: formData.apiKey.trim(),
      endpoint: formData.endpoint.trim() || undefined,
      enabled: true,
    });

    showToast("添加成功");
    onClose();
  };

  return (
    <div className={styles["modal-mask"]}>
      <div className={styles["modal-container"]}>
        <div className={styles["modal-header"]}>
          <h3>添加自定义服务商</h3>
          <button onClick={onClose}>×</button>
        </div>
        <div className={styles["modal-body"]}>
          <div className={styles["form-item"]}>
            <label>服务商名称 *</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="请输入服务商名称"
            />
          </div>
          <div className={styles["form-item"]}>
            <label>服务商类型 *</label>
            <select
              value={formData.type}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
            >
              <option value="openai">OpenAI</option>
              <option value="anthropic">Anthropic</option>
              <option value="google">Google</option>
            </select>
          </div>
          <div className={styles["form-item"]}>
            <label>API Key *</label>
            <input
              type="password"
              value={formData.apiKey}
              onChange={(e) => setFormData({ ...formData, apiKey: e.target.value })}
              placeholder="请输入 API Key"
            />
          </div>
          <div className={styles["form-item"]}>
            <label>自定义端点 (可选)</label>
            <input
              type="text"
              value={formData.endpoint}
              onChange={(e) => setFormData({ ...formData, endpoint: e.target.value })}
              placeholder="留空使用默认端点"
            />
          </div>
        </div>
        <div className={styles["modal-footer"]}>
          <button onClick={onClose}>取消</button>
          <button onClick={handleSubmit} className={styles["primary-button"]}>
            添加
          </button>
        </div>
      </div>
    </div>
  );
}

export function AdminModelServiceSettings() {
  const accessStore = useAccessStore();
  const [showModelManager, setShowModelManager] = useState(false);
  const [currentProvider, setCurrentProvider] = useState<string>("");
  const [showAddCustomProvider, setShowAddCustomProvider] = useState(false);
  const [collapsedProviders, setCollapsedProviders] = useState<Record<ServiceProvider, boolean>>({} as Record<ServiceProvider, boolean>);
  const [collapsedCustomProviders, setCollapsedCustomProviders] = useState<Record<string, boolean>>({});
  const [systemProviders, setSystemProviders] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [frontendConfigDisabled, setFrontendConfigDisabled] = useState(false);

  // 获取系统配置的服务商和配置状态
  useEffect(() => {
    fetchSystemConfig();
  }, []);

  const fetchSystemConfig = async () => {
    setLoading(true);
    try {
      const [providersResponse, configResponse] = await Promise.all([
        authenticatedFetch("/api/admin/providers"),
        authenticatedFetch("/api/admin/system-config"),
      ]);

      if (providersResponse.ok) {
        const data = await providersResponse.json();
        setSystemProviders(data.providers || []);
      }

      if (configResponse.ok) {
        const data = await configResponse.json();
        setFrontendConfigDisabled(data.frontendConfigDisabled || false);
      }
    } catch (error) {
      console.error("Failed to fetch system config:", error);
    } finally {
      setLoading(false);
    }
  };

  // 内置服务商配置
  const builtinProviderConfigs = [
    {
      provider: ServiceProvider.OpenAI,
      name: "OpenAI",
      description: "使用 OpenAI 的 GPT 系列模型",
      isCustom: false,
      configComponent: (
        <>
          <ListItem title="API Key" subTitle="在此处填入从 OpenAI 获取的 API Key">
            <input
              value={accessStore.openaiApiKey}
              type="password"
              placeholder="sk-***"
              onChange={(e) =>
                accessStore.update((access) => (access.openaiApiKey = e.target.value))
              }
            />
          </ListItem>
          <ListItem title="接口地址" subTitle="除默认地址外，可自定义接口地址">
            <input
              value={accessStore.openaiUrl}
              type="text"
              placeholder="https://api.openai.com/v1"
              onChange={(e) =>
                accessStore.update((access) => (access.openaiUrl = e.target.value))
              }
            />
          </ListItem>
        </>
      ),
    },
    {
      provider: ServiceProvider.Anthropic,
      name: "Anthropic",
      description: "使用 Anthropic 的 Claude 系列模型",
      isCustom: false,
      configComponent: (
        <>
          <ListItem title="API Key" subTitle="在此处填入从 Anthropic 获取的 API Key">
            <input
              value={accessStore.anthropicApiKey}
              type="password"
              placeholder="sk-ant-***"
              onChange={(e) =>
                accessStore.update((access) => (access.anthropicApiKey = e.target.value))
              }
            />
          </ListItem>
          <ListItem title="接口地址" subTitle="除默认地址外，可自定义接口地址">
            <input
              value={accessStore.anthropicUrl}
              type="text"
              placeholder="https://api.anthropic.com"
              onChange={(e) =>
                accessStore.update((access) => (access.anthropicUrl = e.target.value))
              }
            />
          </ListItem>
        </>
      ),
    },
    {
      provider: ServiceProvider.Google,
      name: "Google",
      description: "使用 Google 的 Gemini 系列模型",
      isCustom: false,
      configComponent: (
        <>
          <ListItem title="API Key" subTitle="在此处填入从 Google 获取的 API Key">
            <input
              value={accessStore.googleApiKey}
              type="password"
              placeholder="AIza***"
              onChange={(e) =>
                accessStore.update((access) => (access.googleApiKey = e.target.value))
              }
            />
          </ListItem>
          <ListItem title="接口地址" subTitle="除默认地址外，可自定义接口地址">
            <input
              value={accessStore.googleUrl}
              type="text"
              placeholder="https://generativelanguage.googleapis.com"
              onChange={(e) =>
                accessStore.update((access) => (access.googleUrl = e.target.value))
              }
            />
          </ListItem>
        </>
      ),
    },
    // 可以继续添加其他内置服务商...
  ];

  // 自定义服务商配置
  const customProviderConfigs = accessStore.customProviders.map((provider) => ({
    provider: provider.id,
    name: provider.name,
    description: `自定义 ${provider.type} 服务商`,
    isCustom: true,
    configComponent: (
      <>
        <ListItem title="服务商名称">
          <input
            value={provider.name}
            type="text"
            onChange={(e) =>
              accessStore.updateCustomProvider(provider.id, {
                name: e.target.value,
              })
            }
          />
        </ListItem>
        <ListItem title="API Key">
          <input
            value={provider.apiKey}
            type="password"
            placeholder="输入 API Key"
            onChange={(e) =>
              accessStore.updateCustomProvider(provider.id, {
                apiKey: e.target.value,
              })
            }
          />
        </ListItem>
        <ListItem title="接口地址">
          <input
            value={provider.endpoint || ""}
            type="text"
            placeholder="https://api.example.com/v1"
            onChange={(e) =>
              accessStore.updateCustomProvider(provider.id, {
                endpoint: e.target.value,
              })
            }
          />
        </ListItem>
        <ListItem title="操作">
          <IconButton
            text="删除服务商"
            type="danger"
            onClick={async () => {
              if (await showConfirm("确定要删除这个自定义服务商吗？")) {
                accessStore.removeCustomProvider(provider.id);
                showToast("删除成功");
              }
            }}
          />
        </ListItem>
      </>
    ),
  }));

  const providerConfigs = [...builtinProviderConfigs, ...customProviderConfigs];

  // 保存配置到数据库
  const saveProvidersToDatabase = async () => {
    try {
      // 收集所有启用的服务商配置
      const enabledProviders = [];
      
      // 处理内置服务商
      for (const config of builtinProviderConfigs) {
        const isEnabled = accessStore.enabledProviders?.[config.provider as ServiceProvider];
        if (isEnabled) {
          let apiKey = "";
          let baseUrl = "";
          
          switch (config.provider) {
            case ServiceProvider.OpenAI:
              apiKey = accessStore.openaiApiKey;
              baseUrl = accessStore.openaiUrl || "https://api.openai.com/v1";
              break;
            case ServiceProvider.Anthropic:
              apiKey = accessStore.anthropicApiKey;
              baseUrl = accessStore.anthropicUrl || "https://api.anthropic.com";
              break;
            case ServiceProvider.Google:
              apiKey = accessStore.googleApiKey;
              baseUrl = accessStore.googleUrl || "https://generativelanguage.googleapis.com";
              break;
          }
          
          if (apiKey) {
            enabledProviders.push({
              name: config.name,
              providerType: config.provider,
              baseUrl,
              apiKey,
              enabled: true,
            });
          }
        }
      }
      
      // 处理自定义服务商
      for (const provider of accessStore.customProviders) {
        if (provider.enabled && provider.apiKey) {
          enabledProviders.push({
            name: provider.name,
            providerType: provider.type,
            baseUrl: provider.endpoint || "",
            apiKey: provider.apiKey,
            enabled: true,
          });
        }
      }
      
      // 批量保存到数据库
      for (const provider of enabledProviders) {
        await authenticatedFetch("/api/admin/providers", {
          method: "POST",
          body: JSON.stringify(provider),
        });
      }
      
      showToast("配置已保存到数据库");
      fetchSystemConfig();
    } catch (error) {
      showToast("保存失败");
      console.error("Failed to save providers:", error);
    }
  };

  // 切换前端配置禁用状态
  const toggleFrontendConfig = async (disabled: boolean) => {
    try {
      const response = await authenticatedFetch("/api/admin/system-config", {
        method: "POST",
        body: JSON.stringify({ disableFrontendConfig: disabled }),
      });

      if (response.ok) {
        setFrontendConfigDisabled(disabled);
        showToast(disabled ? "已禁用前端配置" : "已启用前端配置");
      } else {
        showToast("操作失败");
      }
    } catch (error) {
      showToast("操作失败");
      console.error("Failed to toggle frontend config:", error);
    }
  };

  return (
    <div className={styles["model-service-admin"]}>
      {/* 系统配置控制 */}
      <div className={styles["system-control-section"]}>
        <div className={styles["control-item"]}>
          <div className={styles["control-info"]}>
            <h3>前端配置模式</h3>
            <p>控制用户是否可以在前端配置服务商信息</p>
          </div>
          <div className={styles["control-action"]}>
            <label className={styles["switch"]}>
              <input
                type="checkbox"
                checked={frontendConfigDisabled}
                onChange={(e) => toggleFrontendConfig(e.target.checked)}
              />
              <span className={styles["slider"]}>
                {frontendConfigDisabled ? "已禁用前端配置" : "允许前端配置"}
              </span>
            </label>
          </div>
        </div>
      </div>

      {/* 系统配置的服务商展示 */}
      {systemProviders.length > 0 && (
        <div className={styles["system-providers-section"]}>
          <h3>当前数据库中的服务商配置</h3>
          <div className={styles["provider-cards"]}>
            {systemProviders.map((provider: any) => (
              <div
                key={provider.id}
                className={`${styles["provider-card"]} ${styles["provider-card-readonly"]}`}
              >
                <div className={styles["provider-card-header"]}>
                  <div className={styles["provider-info"]}>
                    <div className={styles["provider-name"]}>
                      <ProviderIcon provider={provider.providerType} />
                      <span>{provider.name}</span>
                    </div>
                    <div className={styles["provider-status"]}>
                      <span className={styles["status-enabled"]}>✓ 已启用</span>
                    </div>
                  </div>
                </div>
                <div className={styles["provider-config"]}>
                  <div className={styles["config-item"]}>
                    <span>服务地址: {provider.baseUrl}</span>
                  </div>
                  <div className={styles["config-item"]}>
                    <span>API Key: {provider.apiKey}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 前端配置界面 */}
      <div className={styles["frontend-config-section"]}>
        <div className={styles["section-header"]}>
          <h3>前端服务商配置</h3>
          <p>在此配置服务商信息，然后保存到数据库</p>
          <IconButton
            text="保存配置到数据库"
            type="primary"
            onClick={saveProvidersToDatabase}
          />
        </div>
        
        <div className={styles["provider-cards"]}>
          {providerConfigs.map((config) => {
            const isEnabled = config.isCustom
              ? accessStore.customProviders.find((p) => p.id === config.provider)?.enabled || false
              : accessStore.enabledProviders?.[config.provider as ServiceProvider] || false;
            const isCollapsed = config.isCustom
              ? collapsedCustomProviders[config.provider as string] ?? true
              : collapsedProviders[config.provider as ServiceProvider] || false;

            return (
              <div
                key={config.provider}
                className={`${styles["provider-card"]} ${
                  isEnabled ? styles["provider-card-active"] : ""
                }`}
              >
                <div
                  className={styles["provider-card-header"]}
                  onClick={() => {
                    if (isEnabled) {
                      if (config.isCustom) {
                        setCollapsedCustomProviders((prev) => ({
                          ...prev,
                          [config.provider as string]: !prev[config.provider as string],
                        }));
                      } else {
                        setCollapsedProviders((prev) => ({
                          ...prev,
                          [config.provider as ServiceProvider]: !prev[config.provider as ServiceProvider],
                        }));
                      }
                    }
                  }}
                >
                  <div className={styles["provider-info"]}>
                    <span className={styles["provider-icon"]}>
                      <ProviderIcon
                        provider={config.provider}
                        size={24}
                        customProviderType={
                          config.isCustom
                            ? accessStore.customProviders.find((p) => p.id === config.provider)?.type
                            : undefined
                        }
                      />
                    </span>
                    <div>
                      <div className={styles["provider-name-container"]}>
                        <h3 className={styles["provider-name"]}>{config.name}</h3>
                        {isEnabled && (
                          <span className={styles["provider-badge"]}>已启用</span>
                        )}
                      </div>
                      <p className={styles["provider-description"]}>{config.description}</p>
                    </div>
                  </div>
                  <div className={styles["provider-controls"]}>
                    <div className={styles["provider-toggle"]}>
                      <input
                        type="checkbox"
                        checked={isEnabled}
                        onChange={(e) => {
                          e.stopPropagation();
                          if (config.isCustom) {
                            accessStore.updateCustomProvider(config.provider, {
                              enabled: e.target.checked,
                            });
                          } else {
                            accessStore.update((access) => {
                              if (!access.enabledProviders) {
                                access.enabledProviders = {
                                  [ServiceProvider.OpenAI]: false,
                                  [ServiceProvider.Azure]: false,
                                  [ServiceProvider.Google]: false,
                                  [ServiceProvider.Anthropic]: false,
                                  [ServiceProvider.ByteDance]: false,
                                  [ServiceProvider.Alibaba]: false,
                                  [ServiceProvider.Moonshot]: false,
                                  [ServiceProvider.XAI]: false,
                                  [ServiceProvider.DeepSeek]: false,
                                  [ServiceProvider.SiliconFlow]: false,
                                } as Record<ServiceProvider, boolean>;
                              }
                              access.enabledProviders[config.provider as ServiceProvider] = e.target.checked;
                            });
                          }
                        }}
                        className={styles["provider-checkbox"]}
                      />
                    </div>
                    {isEnabled && (
                      <button
                        className={`${styles["collapse-button"]} ${
                          isCollapsed ? styles["collapsed"] : ""
                        }`}
                        onClick={(e) => {
                          e.stopPropagation();
                          if (config.isCustom) {
                            setCollapsedCustomProviders((prev) => ({
                              ...prev,
                              [config.provider as string]: !prev[config.provider as string],
                            }));
                          } else {
                            setCollapsedProviders((prev) => ({
                              ...prev,
                              [config.provider as ServiceProvider]: !prev[config.provider as ServiceProvider],
                            }));
                          }
                        }}
                      >
                        <DownIcon />
                      </button>
                    )}
                  </div>
                </div>

                {isEnabled && (
                  <div
                    className={`${styles["provider-config"]} ${
                      isCollapsed ? styles["collapsed"] : styles["expanded"]
                    }`}
                  >
                    <List>
                      {config.configComponent}

                      {/* 启用模型列表 */}
                      <ListItem
                        title="启用的模型"
                        subTitle="当前服务商中已启用的模型列表"
                      >
                        <div className={styles["enabled-models"]}>
                          <div className={styles["model-list"]}>
                            {(accessStore.enabledModels?.[config.provider] || []).length > 0 ? (
                              <div className={styles["model-tags"]}>
                                {(accessStore.enabledModels?.[config.provider] || []).map((modelName: string) => (
                                  <span key={modelName} className={styles["model-tag"]}>
                                    {modelName}
                                    <ModelCapabilityIcons
                                      capabilities={getModelCapabilitiesWithCustomConfig(modelName)}
                                      size={12}
                                      colorful={false}
                                    />
                                  </span>
                                ))}
                              </div>
                            ) : (
                              <span className={styles["no-models"]}>暂无启用的模型</span>
                            )}
                          </div>
                          <button
                            className={styles["manage-button"]}
                            onClick={() => {
                              setCurrentProvider(config.provider);
                              setShowModelManager(true);
                            }}
                          >
                            管理
                          </button>
                        </div>
                      </ListItem>
                    </List>
                  </div>
                )}
              </div>
            );
          })}

          {/* 添加自定义服务商按钮 */}
          <div className={styles["add-custom-provider"]}>
            <button
              className={styles["add-custom-provider-button"]}
              onClick={() => setShowAddCustomProvider(true)}
            >
              <span className={styles["add-icon"]}>+</span>
              <div className={styles["add-text"]}>
                <h3>添加自定义服务商</h3>
                <p>添加自定义的 AI 服务商配置</p>
              </div>
            </button>
          </div>
        </div>
      </div>

      {/* 模型管理器 */}
      {showModelManager && (
        <ModelManager
          provider={currentProvider}
          onClose={() => setShowModelManager(false)}
        />
      )}

      {/* 添加自定义服务商 */}
      {showAddCustomProvider && (
        <AddCustomProviderModal onClose={() => setShowAddCustomProvider(false)} />
      )}
    </div>
  );
}
