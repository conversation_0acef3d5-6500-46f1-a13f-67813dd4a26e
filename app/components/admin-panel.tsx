import React, { useState, useEffect } from "react";
import { useAdminStore } from "../store/admin";
import { useUserStore } from "../store/user";
import styles from "./admin-panel.module.scss";
import { IconButton } from "./button";
import { showConfirm, showToast } from "./ui-lib";
import { Modal, Input, Form, Select, Switch } from "antd";
import { ServiceProvider } from "../constant";
import { AdminModelServiceSettings } from "./admin-model-service";

// 通用的认证 API 请求函数
const authenticatedFetch = async (url: string, options: RequestInit = {}) => {
  const userStore = useUserStore.getState();
  const token = userStore.token;

  if (!token) {
    throw new Error("No authentication token");
  }

  return fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      "Authorization": `Bearer ${token}`,
      "Content-Type": "application/json",
    },
  });
};

// 服务商模态框组件
function ProviderModal({ open, provider, onClose, onSuccess }: {
  open: boolean;
  provider?: any;
  onClose: () => void;
  onSuccess: () => void;
}) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (provider) {
      form.setFieldsValue(provider);
    } else {
      form.resetFields();
    }
  }, [provider, form]);

  const handleSubmit = async (values: any) => {
    setLoading(true);
    try {
      const requestData = provider ? { ...values, id: provider.id } : values;

      const response = await authenticatedFetch("/api/admin/providers", {
        method: "POST",
        body: JSON.stringify(requestData),
      });

      if (response.ok) {
        showToast(provider ? "更新成功" : "添加成功");
        onSuccess();
      } else {
        showToast(provider ? "更新失败" : "添加失败");
      }
    } catch (error) {
      showToast("操作失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title={provider ? "编辑服务商" : "添加服务商"}
      open={open}
      onCancel={onClose}
      footer={null}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{ enabled: true }}
      >
        <Form.Item
          name="name"
          label="服务商名称"
          rules={[{ required: true, message: "请输入服务商名称" }]}
        >
          <Input placeholder="例如：OpenAI" />
        </Form.Item>

        <Form.Item
          name="providerType"
          label="服务商类型"
          rules={[{ required: true, message: "请选择服务商类型" }]}
        >
          <Select placeholder="选择服务商类型">
            <Select.Option value="openai">OpenAI</Select.Option>
            <Select.Option value="anthropic">Anthropic</Select.Option>
            <Select.Option value="google">Google</Select.Option>
            <Select.Option value="azure">Azure</Select.Option>
            <Select.Option value="custom">自定义</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="baseUrl"
          label="API 基础地址"
          rules={[{ required: true, message: "请输入API基础地址" }]}
        >
          <Input placeholder="https://api.openai.com/v1" />
        </Form.Item>

        <Form.Item
          name="apiKey"
          label="API Key"
          rules={[{ required: true, message: "请输入API Key" }]}
        >
          <Input.Password placeholder="输入API Key" />
        </Form.Item>

        <Form.Item name="enabled" valuePropName="checked">
          <Switch /> 启用此服务商
        </Form.Item>

        <Form.Item>
          <div style={{ display: "flex", gap: "8px", justifyContent: "flex-end" }}>
            <IconButton text="取消" onClick={onClose} />
            <IconButton
              text={loading ? "处理中..." : (provider ? "更新" : "添加")}
              type="primary"
              onClick={() => form.submit()}
              disabled={loading}
            />
          </div>
        </Form.Item>
      </Form>
    </Modal>
  );
}

// 仪表板面板
function DashboardPanel() {
  const [stats, setStats] = useState({
    userCount: 0,
    providerCount: 0,
    todayCalls: 0,
    totalCost: 0,
  });

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      const response = await authenticatedFetch("/api/admin/dashboard");
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats || stats);
      }
    } catch (error) {
      console.error("Failed to fetch dashboard stats:", error);
    }
  };

  return (
    <div className={styles["admin-panel-content"]}>
      <div className={styles["admin-panel-header"]}>
        <h2>📊 仪表板</h2>
        <p>系统概览和统计信息</p>
      </div>
      <div className={styles["admin-panel-body"]}>
        <div className={styles["stats-grid"]}>
          <div className={styles["stat-card"]}>
            <div className={styles["stat-icon"]}>👥</div>
            <div className={styles["stat-content"]}>
              <div className={styles["stat-number"]}>{stats.userCount}</div>
              <div className={styles["stat-label"]}>用户总数</div>
            </div>
          </div>
          <div className={styles["stat-card"]}>
            <div className={styles["stat-icon"]}>🤖</div>
            <div className={styles["stat-content"]}>
              <div className={styles["stat-number"]}>{stats.providerCount}</div>
              <div className={styles["stat-label"]}>服务商数量</div>
            </div>
          </div>
          <div className={styles["stat-card"]}>
            <div className={styles["stat-icon"]}>📞</div>
            <div className={styles["stat-content"]}>
              <div className={styles["stat-number"]}>{stats.todayCalls}</div>
              <div className={styles["stat-label"]}>今日调用</div>
            </div>
          </div>
          <div className={styles["stat-card"]}>
            <div className={styles["stat-icon"]}>💰</div>
            <div className={styles["stat-content"]}>
              <div className={styles["stat-number"]}>¥{stats.totalCost}</div>
              <div className={styles["stat-label"]}>总消费</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function ProvidersPanel() {
  return (
    <div className={styles["admin-panel-content"]}>
      <div className={styles["admin-panel-header"]}>
        <h2>🤖 服务商管理</h2>
        <p>配置和管理 AI 服务商</p>
      </div>
      <div className={styles["admin-panel-body"]}>
        <AdminModelServiceSettings />
      </div>
    </div>
  );
}

function UsersPanel() {
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const response = await authenticatedFetch("/api/admin/users");
      if (response.ok) {
        const data = await response.json();
        setUsers(data.users || []);
      }
    } catch (error) {
      console.error("Failed to fetch users:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleUserStatus = async (id: number, status: string) => {
    try {
      const response = await authenticatedFetch(`/api/admin/users?id=${id}`, {
        method: "PATCH",
        body: JSON.stringify({ status }),
      });
      if (response.ok) {
        showToast("状态更新成功");
        fetchUsers();
      }
    } catch (error) {
      showToast("操作失败");
    }
  };

  const handleDeleteUser = async (id: number) => {
    if (await showConfirm("确定要删除这个用户吗？")) {
      try {
        const response = await authenticatedFetch(`/api/admin/users?id=${id}`, {
          method: "DELETE",
        });
        if (response.ok) {
          showToast("删除成功");
          fetchUsers();
        } else {
          showToast("删除失败");
        }
      } catch (error) {
        showToast("删除失败");
      }
    }
  };

  return (
    <div className={styles["admin-panel-content"]}>
      <div className={styles["admin-panel-header"]}>
        <h2>👥 用户管理</h2>
        <p>管理用户账号和权限</p>
      </div>
      <div className={styles["admin-panel-body"]}>
        {loading ? (
          <div className={styles["loading"]}>加载中...</div>
        ) : (
          <div className={styles["user-list"]}>
            {users.map((user) => (
              <div key={user.id} className={styles["user-card"]}>
                <div className={styles["user-info"]}>
                  <div className={styles["user-name"]}>{user.username}</div>
                  <div className={styles["user-email"]}>{user.email}</div>
                  <div className={styles["user-role"]}>
                    角色: {user.role === "admin" ? "管理员" : "普通用户"}
                  </div>
                  <div className={styles["user-quota"]}>
                    配额: {user.usedQuota}/{user.apiQuota}
                  </div>
                </div>
                <div className={styles["user-actions"]}>
                  <Select
                    value={user.status}
                    onChange={(value) => handleToggleUserStatus(user.id, value)}
                    style={{ width: 100 }}
                  >
                    <Select.Option value="active">正常</Select.Option>
                    <Select.Option value="disabled">禁用</Select.Option>
                  </Select>
                  <IconButton
                    text="删除"
                    onClick={() => handleDeleteUser(user.id)}
                    type="danger"
                  />
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function ConfigPanel() {
  const [configs, setConfigs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const response = await authenticatedFetch("/api/admin/config");
      if (response.ok) {
        const data = await response.json();
        setConfigs(data.configs || []);
      }
    } catch (error) {
      console.error("Failed to fetch configs:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateConfig = async (key: string, value: any) => {
    try {
      const response = await authenticatedFetch("/api/admin/config", {
        method: "PUT",
        body: JSON.stringify({ key, value }),
      });
      if (response.ok) {
        showToast("配置更新成功");
        fetchConfigs();
      }
    } catch (error) {
      showToast("更新失败");
    }
  };

  const renderConfigValue = (config: any) => {
    switch (config.configType) {
      case "boolean":
        return (
          <Switch
            checked={config.configValue === "true"}
            onChange={(checked) =>
              handleUpdateConfig(config.configKey, checked.toString())
            }
          />
        );
      case "number":
        return (
          <Input
            type="number"
            value={config.configValue}
            onChange={(e) =>
              handleUpdateConfig(config.configKey, e.target.value)
            }
            style={{ width: 200 }}
          />
        );
      default:
        return (
          <Input
            value={config.configValue}
            onChange={(e) =>
              handleUpdateConfig(config.configKey, e.target.value)
            }
            style={{ width: 200 }}
          />
        );
    }
  };

  return (
    <div className={styles["admin-panel-content"]}>
      <div className={styles["admin-panel-header"]}>
        <h2>⚙️ 系统配置</h2>
        <p>全局系统设置和参数</p>
      </div>
      <div className={styles["admin-panel-body"]}>
        {loading ? (
          <div className={styles["loading"]}>加载中...</div>
        ) : (
          <div className={styles["config-list"]}>
            {configs.map((config) => (
              <div key={config.configKey} className={styles["config-item"]}>
                <div className={styles["config-info"]}>
                  <div className={styles["config-key"]}>{config.configKey}</div>
                  <div className={styles["config-desc"]}>{config.description}</div>
                </div>
                <div className={styles["config-value"]}>
                  {renderConfigValue(config)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

function LogsPanel() {
  const [logs, setLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, limit: 20, total: 0 });

  useEffect(() => {
    fetchLogs();
  }, [pagination.page]);

  const fetchLogs = async () => {
    setLoading(true);
    try {
      const response = await authenticatedFetch(
        `/api/admin/logs?page=${pagination.page}&limit=${pagination.limit}`
      );
      if (response.ok) {
        const data = await response.json();
        setLogs(data.logs || []);
        setPagination(prev => ({ ...prev, total: data.total || 0 }));
      }
    } catch (error) {
      console.error("Failed to fetch logs:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "success": return "#52c41a";
      case "error": return "#ff4d4f";
      default: return "#1890ff";
    }
  };

  return (
    <div className={styles["admin-panel-content"]}>
      <div className={styles["admin-panel-header"]}>
        <h2>📝 调用日志</h2>
        <p>API 使用记录和统计</p>
        <IconButton text="刷新" onClick={fetchLogs} />
      </div>
      <div className={styles["admin-panel-body"]}>
        {loading ? (
          <div className={styles["loading"]}>加载中...</div>
        ) : (
          <>
            <div className={styles["log-list"]}>
              {logs.map((log) => (
                <div key={log.id} className={styles["log-item"]}>
                  <div className={styles["log-info"]}>
                    <div className={styles["log-model"]}>{log.modelName}</div>
                    <div className={styles["log-provider"]}>{log.providerId}</div>
                    <div className={styles["log-time"]}>{formatDate(log.createdAt)}</div>
                  </div>
                  <div className={styles["log-details"]}>
                    <div className={styles["log-tokens"]}>
                      Tokens: {log.totalTokens || 0}
                    </div>
                    <div
                      className={styles["log-status"]}
                      style={{ color: getStatusColor(log.status) }}
                    >
                      {log.status}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* 分页 */}
            <div className={styles["pagination"]}>
              <IconButton
                text="上一页"
                onClick={() => setPagination(prev => ({
                  ...prev,
                  page: Math.max(1, prev.page - 1)
                }))}
                disabled={pagination.page <= 1}
              />
              <span className={styles["page-info"]}>
                第 {pagination.page} 页，共 {Math.ceil(pagination.total / pagination.limit)} 页
              </span>
              <IconButton
                text="下一页"
                onClick={() => setPagination(prev => ({
                  ...prev,
                  page: prev.page + 1
                }))}
                disabled={pagination.page >= Math.ceil(pagination.total / pagination.limit)}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
}

export function AdminPanel() {
  const adminStore = useAdminStore();

  const renderContent = () => {
    switch (adminStore.activeTab) {
      case "dashboard":
        return <DashboardPanel />;
      case "providers":
        return <ProvidersPanel />;
      case "users":
        return <UsersPanel />;
      case "config":
        return <ConfigPanel />;
      case "logs":
        return <LogsPanel />;
      default:
        return <DashboardPanel />;
    }
  };

  return (
    <div className={styles["admin-panel"]}>
      {renderContent()}
    </div>
  );
}
