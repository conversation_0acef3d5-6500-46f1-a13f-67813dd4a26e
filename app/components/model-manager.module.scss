/* 自定义Modal样式 */
.custom-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
}

.custom-modal {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 85vw;
  max-width: 1000px;
  height: 80vh;
  max-height: 800px;
  min-height: 600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  &.maximized {
    width: 95vw;
    height: 95vh;
    max-width: none;
    max-height: none;
  }
}

.custom-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 2px solid var(--second);
  background: var(--white);
  flex-shrink: 0;
}

.custom-modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin: 0;
}

.custom-modal-actions {
  display: flex;
  gap: 20px; // 与原Modal保持一致的间距
}

.custom-modal-action {
  // 参考对话设置modal的按钮样式
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  background: transparent;
  border: none;
  border-radius: 6px;
  transition: all 0.2s ease;
  color: var(--black);

  &:hover {
    filter: brightness(1.2); // 与对话设置modal一致的悬停效果
    background: var(--hover-color);
  }

  svg {
    width: 16px;
    height: 16px;
    opacity: 0.8;
    transition: all 0.2s ease;
    filter: none !important; // 确保不受全局暗夜模式影响
  }

  &:hover svg {
    opacity: 1;
  }

  // 确保no-dark类生效
  &.no-dark svg {
    filter: none !important;
  }
}

.custom-modal-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.model-manager {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  padding: 24px;

  // 强制重置搜索区域的布局
  .search-section {
    display: flex !important;
    flex-direction: row !important;
    align-items: stretch !important;
    gap: 12px !important;
    width: 100% !important;

    .search-input {
      flex: 1 !important;
      min-width: 0 !important;
      width: auto !important;
      max-width: none !important;
    }

    .add-custom-button {
      flex-shrink: 0 !important;
      width: auto !important;
      min-width: auto !important;
    }
  }
}

// 重新设计搜索区域布局 - 使用更具体的选择器
.model-manager .search-section {
  margin-bottom: 24px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  width: 100% !important;
  gap: 12px !important;
  align-items: stretch !important; // 改为stretch确保高度一致
  box-sizing: border-box !important;
  justify-content: space-between !important;
}

.search-section {
  margin-bottom: 24px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  width: 100% !important;
  gap: 12px !important;
  align-items: stretch !important;
  box-sizing: border-box !important;
  justify-content: space-between !important;
}

// 搜索输入框样式
.model-manager .search-section .search-input,
.search-input {
  box-sizing: border-box !important;
  padding: 14px 18px !important; // 稍微减小padding与按钮匹配
  border: 2px solid var(--border-in-light) !important;
  border-radius: 10px !important; // 与按钮保持一致
  background: var(--second) !important;
  color: var(--black) !important;
  font-size: 14px !important; // 与按钮字体大小匹配
  text-align: left !important;
  transition: all 0.2s ease !important;
  display: block !important;
  min-width: 0 !important;
  flex: 1 !important; // 占满剩余空间
  width: auto !important;
  max-width: none !important;
  height: auto !important;

  &:focus {
    outline: none !important;
    border-color: var(--primary) !important;
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1) !important;
    background: var(--white) !important;
  }

  &::placeholder {
    color: var(--black) !important;
    opacity: 0.6 !important;
  }
}

// 添加自定义模型按钮样式 - 优化为更明显的按钮外观
.model-manager .search-section .add-custom-button,
.add-custom-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 14px 18px !important; // 与搜索框高度匹配
  border: 2px solid var(--primary) !important;
  border-radius: 10px !important;
  background: var(--primary) !important;
  color: white !important;
  font-size: 13px !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  min-width: auto !important;
  width: auto !important;
  height: auto !important;
  box-shadow: 0 1px 3px rgba(29, 147, 171, 0.2) !important;

  &:hover {
    background: var(--primary) !important;
    filter: brightness(1.1) !important;
    box-shadow: 0 3px 8px rgba(29, 147, 171, 0.25) !important;
    transform: translateY(-1px) !important;
  }

  &:active {
    transform: translateY(0px) !important;
    box-shadow: 0 1px 3px rgba(29, 147, 171, 0.2) !important;
  }
}

.category-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--second);
  flex-shrink: 0;
}

.category-tab {
  padding: 8px 16px;
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  color: var(--black);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: var(--hover-color);
    border-color: var(--primary);
    transform: translateY(-1px);
  }

  &.active {
    background: var(--primary);
    color: white;
    border-color: var(--primary);
    box-shadow: 0 2px 4px rgba(29, 147, 171, 0.2);
  }
}

.model-list {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; // 确保flex子元素可以收缩
}

.category-section {
  margin-bottom: 24px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.category-header {
  margin-bottom: 12px;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
    padding: 8px 0;
    border-bottom: 2px solid var(--primary);
    display: inline-block;
  }
}

.model-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.model-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--hover-color);
    box-shadow: var(--card-shadow);
  }
}

.model-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.model-icon {
  font-size: 20px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--second);
  border-radius: 6px;
  flex-shrink: 0;
}

.model-details {
  flex: 1;
  min-width: 0;
}

.model-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  margin-bottom: 2px;
  display: flex;
  align-items: center;
  min-width: 0;
  // 确保在暗夜模式下文字清晰可见
  opacity: 1;
  
  // 模型名称文本部分
  > span:first-child,
  > :first-child:not(.capability-icons) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    flex: 1;
    min-width: 0;
  }
}

.model-id {
  font-size: 12px;
  color: var(--black);
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  // 在暗夜模式下提高副标题的可见度
}

// 模型操作按钮容器
.model-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
}

// 响应时间显示
.response-time {
  font-size: 11px;
  color: #10b981;
  font-weight: 500;
  white-space: nowrap;
  margin-right: 2px;
}

// 错误显示容器
.error-display {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-right: 2px;
}

// 错误信息显示
.error-info {
  font-size: 10px;
  color: #dc2626;
  font-weight: 600;
  white-space: nowrap;
  padding: 2px 4px;
  background: #fee2e2;
  border-radius: 3px;
  border: 1px solid #fecaca;
  cursor: help;
  transition: all 0.2s ease;

  &:hover {
    background: #fecaca;
    border-color: #f87171;
    transform: scale(1.05);
  }
}

// 控制台提示文字
.console-tip {
  font-size: 9px;
  color: #dc2626;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0.8;
  cursor: pointer;
  padding: 1px 3px;
  border-radius: 2px;
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    background: #fee2e2;
    transform: scale(1.05);
  }
}

// 测试按钮样式
.test-button {
  padding: 4px 8px;
  border: 1px solid var(--border-in-light);
  border-radius: 4px;
  background: var(--white);
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-size: 11px;
  font-weight: 500;
  min-width: 36px;
  height: 24px;

  &:hover {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.6;
  }

  &.testing {
    border-color: #f59e0b;
    background: #fef3c7;
    color: #d97706;

    svg {
      width: 12px;
      height: 12px;
      animation: spin 1s linear infinite;
    }
  }

  &.success {
    border-color: #10b981;
    background: #d1fae5;
    color: #059669;
  }

  &.error {
    border-color: #ef4444;
    background: #fee2e2;
    color: #dc2626;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// 管理按钮样式
.manage-button {
  width: 28px;
  height: 28px;
  border: 1px solid var(--border-in-light);
  border-radius: 6px;
  background: var(--white);
  color: var(--black);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  opacity: 0.7;
  padding: 0;
  box-sizing: border-box;

  &:hover {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
    opacity: 1;
  }

  svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
    display: block;
    flex-shrink: 0;
  }
}

.toggle-button {
  width: 32px;
  height: 32px;
  border: 2px solid var(--border-in-light);
  border-radius: 50%;
  background: var(--white);
  color: var(--black);
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;

  &:hover {
    border-color: var(--primary);
    background: var(--primary);
    color: white;
  }

  &.enabled {
    border-color: var(--primary);
    background: var(--primary);
    color: white;

    &:hover {
      background: #d32f2f;
      border-color: #d32f2f;
    }
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: var(--black);
  opacity: 0.6;

  p {
    margin: 0;
    font-size: 14px;
  }
}

// 自定义模型表单样式 - 优化为更简洁的设计
.custom-model-form {
  margin-bottom: 20px;
  padding: 24px;
  border: 1px solid var(--border-in-light);
  border-radius: 12px;
  background: var(--second);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.form-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--border-in-light);

  h4 {
    margin: 0;
    font-size: 15px;
    font-weight: 600;
    color: var(--black);
    opacity: 0.9;
  }
}

.form-close {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--hover-color);
  }

  svg {
    width: 14px;
    height: 14px;
    opacity: 0.6;
  }
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;

  label {
    font-size: 14px;
    font-weight: 500;
    color: var(--black);
  }
}

.form-input {
  padding: 14px 16px;
  border: 1px solid var(--border-in-light);
  border-radius: 10px;
  background: var(--white);
  color: var(--black);
  font-size: 14px;
  transition: all 0.2s ease;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.08);
    background: var(--white);
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

.form-hint {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  margin-top: 4px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 8px;
}

.form-cancel,
.form-submit {
  padding: 12px 24px;
  border-radius: 10px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.form-cancel {
  background: var(--white);
  color: var(--black);
  border-color: var(--border-in-light);

  &:hover {
    background: var(--second);
    border-color: var(--primary);
  }
}

.form-submit {
  background: var(--primary);
  color: white;
  border-color: var(--primary);

  &:hover {
    filter: brightness(1.1);
    box-shadow: 0 2px 6px rgba(29, 147, 171, 0.2);
  }

  &:active {
    transform: translateY(1px);
  }
}

// 模型配置弹窗样式
.model-config-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  padding: 20px;
}

.config-modal-content {
  background: var(--white);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.config-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-in-light);
  flex-shrink: 0;

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--black);
  }
}

.config-close {
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background: var(--hover-color);
  }

  svg {
    width: 14px;
    height: 14px;
    opacity: 0.6;
  }
}

.config-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px 24px;
}

.config-section {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }

  h5 {
    margin: 0 0 16px 0;
    font-size: 14px;
    font-weight: 600;
    color: var(--black);
    opacity: 0.9;
  }
}

.config-field {
  margin-bottom: 16px;

  &:last-child {
    margin-bottom: 0;
  }

  label {
    display: block;
    margin-bottom: 8px;
    font-size: 13px;
    font-weight: 500;
    color: var(--black);
  }
}

.config-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid var(--border-in-light);
  border-radius: 8px;
  background: var(--white);
  color: var(--black);
  font-size: 14px;
  transition: all 0.2s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(29, 147, 171, 0.1);
  }

  &:disabled {
    background: var(--second);
    color: var(--black);
    opacity: 0.6;
    cursor: not-allowed;
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

// 能力配置样式 - 使用点状效果
.capabilities-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 10px 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
  user-select: none;
  border: 1px solid transparent;

  &:hover {
    background: var(--second);
    border-color: var(--border-in-light);
  }

  &:active {
    transform: scale(0.98);
  }
}

// 能力点状指示器
.capability-dot {
  width: 12px;
  height: 12px;
  border: 2px solid var(--border-in-light);
  border-radius: 50%;
  background: var(--white);
  transition: all 0.2s ease;
  flex-shrink: 0;
  position: relative;
  pointer-events: none; // 禁用圆点自身的点击事件

  &.active {
    background: var(--primary);
    border-color: var(--primary);

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 4px;
      height: 4px;
      background: white;
      border-radius: 50%;
    }
  }

  // 当父元素悬停时的效果
  .capability-item:hover & {
    border-color: var(--primary);
    transform: scale(1.1);
  }
}

.capability-text {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--black);
  user-select: none;
}

.capability-icon {
  font-size: 14px;
}

// 配置操作按钮
.config-actions {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 24px;
  border-top: 1px solid var(--border-in-light);
  flex-shrink: 0;
  position: relative;
}

.config-delete {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #d32f2f;
  border-radius: 6px;
  background: transparent;
  color: #d32f2f;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  position: absolute;
  left: 24px;

  &:hover {
    background: #d32f2f;
    color: white;
  }

  svg {
    width: 12px;
    height: 12px;
    fill: currentColor;
  }
}

.config-buttons {
  display: flex;
  gap: 12px;
}

.config-cancel,
.config-save {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.config-cancel {
  background: var(--second);
  color: var(--black);
  border-color: var(--border-in-light);

  &:hover {
    background: var(--hover-color);
    border-color: var(--primary);
  }
}

.config-save {
  background: var(--primary);
  color: white;
  border-color: var(--primary);

  &:hover {
    filter: brightness(1.1);
    box-shadow: 0 2px 4px rgba(29, 147, 171, 0.2);
  }
}

/* 滚动条样式 */
.model-list::-webkit-scrollbar {
  width: 8px;
}

.model-list::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.model-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  transition: background 0.2s ease;

  &:hover {
    background: var(--primary);
  }
}

.model-list::-webkit-scrollbar-corner {
  background: transparent;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .custom-modal {
    width: 95vw;
    height: 90vh;
    margin: 20px;
  }

  .model-manager {
    padding: 16px;
  }

  .search-section {
    flex-direction: row !important; // 保持水平布局
    gap: 8px !important;
  }

  .search-input {
    padding: 12px 14px !important;
    font-size: 13px !important;
  }

  .add-custom-button {
    padding: 12px 14px !important;
    font-size: 12px !important;
    min-width: auto !important;
  }

  .response-time {
    font-size: 10px !important;
    margin-right: 1px !important;
  }

  .error-display {
    gap: 2px !important;
    margin-right: 1px !important;
  }

  .error-info {
    font-size: 9px !important;
    padding: 1px 3px !important;
  }

  .console-tip {
    font-size: 8px !important;
    padding: 1px 2px !important;
  }

  .test-button {
    padding: 3px 6px !important;
    font-size: 10px !important;
    min-width: 32px !important;
    height: 22px !important;
  }

  .manage-button {
    width: 24px !important;
    height: 24px !important;

    svg {
      width: 12px !important;
      height: 12px !important;
    }
  }

  .toggle-button {
    width: 28px !important;
    height: 28px !important;
    font-size: 16px !important;
  }

  .config-modal-content {
    margin: 10px;
    max-height: 90vh;
  }

  .config-header {
    padding: 16px 20px;
  }

  .config-content {
    padding: 16px 20px;
  }

  .capabilities-grid {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .config-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
    padding: 16px 20px;

    .config-buttons {
      width: 100%;

      .config-cancel,
      .config-save {
        flex: 1;
      }
    }
  }
}

  .category-tabs {
    gap: 8px;
  }

  .category-tab {
    padding: 6px 12px;
    font-size: 12px;
  }

  .custom-model-form {
    padding: 16px;
  }

  .form-actions {
    flex-direction: column;

    .form-cancel,
    .form-submit {
      width: 100%;
    }
  }

/* 响应式设计 */
@media screen and (max-width: 600px) {
  .model-manager {
    height: 60vh;
  }
  
  .category-tabs {
    gap: 6px;
  }
  
  .category-tab {
    padding: 4px 8px;
    font-size: 11px;
  }
  
  .model-item {
    padding: 10px 12px;
  }
  
  .model-icon {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
  
  .toggle-button {
    width: 28px;
    height: 28px;
    font-size: 16px;
  }
}

/* 暗夜模式优化 - 参考对话设置modal的简洁处理 */
.dark {
  .custom-modal-action {
    &:hover {
      background: rgba(255, 255, 255, 0.1); // 暗夜模式下的悬停背景
    }
  }

  .model-name {
    // 暗夜模式下确保标题文字足够亮
    opacity: 1;
    color: var(--black);
  }

  .model-id {
    // 暗夜模式下副标题也要清晰
    opacity: 0.9;
    color: var(--black);
  }

  .custom-modal-title {
    // 暗夜模式下标题更清晰
    color: var(--black);
    opacity: 1;
  }
}

/* 确保模型管理器中的能力图标不受暗夜模式影响 */
.custom-modal {
  // 覆盖全局的 div:not(.no-dark) > svg 规则
  .no-dark svg {
    filter: none !important;
  }

  // 专门针对能力图标的样式
  .model-name {
    .no-dark svg {
      filter: none !important;
      opacity: 1;
    }
  }

  // 在暗夜模式下也确保能力图标正常显示
  :global(.dark) & {
    .no-dark svg {
      filter: none !important;
      opacity: 1;
    }
  }
}
