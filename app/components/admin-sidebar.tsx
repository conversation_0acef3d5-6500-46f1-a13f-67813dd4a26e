import React from "react";
import { useAdminStore, AdminPanelTab } from "../store/admin";
import styles from "./admin-sidebar.module.scss";
import clsx from "clsx";

interface AdminMenuItem {
  key: AdminPanelTab;
  icon: string;
  title: string;
  description: string;
}

const adminMenuItems: AdminMenuItem[] = [
  {
    key: "dashboard",
    icon: "📊",
    title: "仪表板",
    description: "系统概览和统计",
  },
  {
    key: "providers",
    icon: "🤖",
    title: "服务商管理",
    description: "AI 服务商配置",
  },
  {
    key: "users",
    icon: "👥",
    title: "用户管理",
    description: "用户账号和权限",
  },
  {
    key: "config",
    icon: "⚙️",
    title: "系统配置",
    description: "全局系统设置",
  },
  {
    key: "logs",
    icon: "📝",
    title: "调用日志",
    description: "API 使用记录",
  },
];

interface AdminSidebarProps {
  narrow?: boolean;
}

export function AdminSidebar({ narrow }: AdminSidebarProps) {
  const adminStore = useAdminStore();

  return (
    <div className={styles["admin-sidebar"]}>
      <div className={styles["admin-menu"]}>
        {adminMenuItems.map((item) => (
          <div
            key={item.key}
            className={clsx(styles["admin-menu-item"], {
              [styles["admin-menu-item-active"]]: adminStore.activeTab === item.key,
              [styles["admin-menu-item-narrow"]]: narrow,
            })}
            onClick={() => adminStore.setActiveTab(item.key)}
          >
            <div className={styles["admin-menu-icon"]}>{item.icon}</div>
            {!narrow && (
              <div className={styles["admin-menu-content"]}>
                <div className={styles["admin-menu-title"]}>{item.title}</div>
                <div className={styles["admin-menu-description"]}>
                  {item.description}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}
