import { useState } from "react";
import { Modal, Input, Button, Form, message, Tabs } from "antd";
import { UserOutlined, LockOutlined, MailOutlined } from "@ant-design/icons";
import { useUserStore } from "../store/user";
import Locale from "../locales";

interface AuthModalProps {
  open: boolean;
  onClose: () => void;
}

export function AuthModal({ open, onClose }: AuthModalProps) {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("login");
  const userStore = useUserStore();

  const handleLogin = async (values: { username: string; password: string }) => {
    setLoading(true);
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (data.success) {
        userStore.login(data.token, data.user);
        message.success("登录成功");
        onClose();
      } else {
        message.error(data.error || "登录失败");
      }
    } catch (error) {
      message.error("网络错误，请重试");
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (values: {
    username: string;
    email: string;
    password: string;
  }) => {
    setLoading(true);
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (data.success) {
        message.success("注册成功，请登录");
        setActiveTab("login");
      } else {
        message.error(data.error || "注册失败");
      }
    } catch (error) {
      message.error("网络错误，请重试");
    } finally {
      setLoading(false);
    }
  };

  const loginForm = (
    <Form onFinish={handleLogin} layout="vertical" size="large">
      <Form.Item
        name="username"
        rules={[{ required: true, message: "请输入用户名" }]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名"
          style={{
            backgroundColor: "var(--gray)",
            border: "1px solid var(--border-color)",
            borderRadius: "8px",
          }}
        />
      </Form.Item>
      <Form.Item
        name="password"
        rules={[{ required: true, message: "请输入密码" }]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
          style={{
            backgroundColor: "var(--gray)",
            border: "1px solid var(--border-color)",
            borderRadius: "8px",
          }}
        />
      </Form.Item>
      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
          style={{
            height: "48px",
            borderRadius: "8px",
            fontSize: "16px",
            fontWeight: "500",
          }}
        >
          登录
        </Button>
      </Form.Item>
    </Form>
  );

  const registerForm = (
    <Form onFinish={handleRegister} layout="vertical" size="large">
      <Form.Item
        name="username"
        rules={[{ required: true, message: "请输入用户名" }]}
      >
        <Input
          prefix={<UserOutlined />}
          placeholder="用户名"
          style={{
            backgroundColor: "var(--gray)",
            border: "1px solid var(--border-color)",
            borderRadius: "8px",
          }}
        />
      </Form.Item>
      <Form.Item
        name="email"
        rules={[
          { required: true, message: "请输入邮箱" },
          { type: "email", message: "请输入有效的邮箱地址" },
        ]}
      >
        <Input
          prefix={<MailOutlined />}
          placeholder="邮箱"
          style={{
            backgroundColor: "var(--gray)",
            border: "1px solid var(--border-color)",
            borderRadius: "8px",
          }}
        />
      </Form.Item>
      <Form.Item
        name="password"
        rules={[
          { required: true, message: "请输入密码" },
          { min: 6, message: "密码至少6位" },
        ]}
      >
        <Input.Password
          prefix={<LockOutlined />}
          placeholder="密码"
          style={{
            backgroundColor: "var(--gray)",
            border: "1px solid var(--border-color)",
            borderRadius: "8px",
          }}
        />
      </Form.Item>
      <Form.Item>
        <Button
          type="primary"
          htmlType="submit"
          loading={loading}
          block
          style={{
            height: "48px",
            borderRadius: "8px",
            fontSize: "16px",
            fontWeight: "500",
          }}
        >
          注册
        </Button>
      </Form.Item>
    </Form>
  );

  const tabItems = [
    {
      key: "login",
      label: "登录",
      children: loginForm,
    },
    {
      key: "register",
      label: "注册",
      children: registerForm,
    },
  ];

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      width={400}
      centered
      styles={{
        content: {
          backgroundColor: "var(--white)",
          borderRadius: "16px",
          padding: "32px",
        },
      }}
    >
      <div style={{ textAlign: "center", marginBottom: "32px" }}>
        <div
          style={{
            fontSize: "32px",
            fontWeight: "bold",
            marginBottom: "8px",
            background: "linear-gradient(45deg, #1890ff, #722ed1)",
            WebkitBackgroundClip: "text",
            WebkitTextFillColor: "transparent",
          }}
        >
          🤖 QADChat
        </div>
        <div style={{ color: "var(--black)", fontSize: "16px" }}>
          AI 聊天助手
        </div>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        centered
      />
    </Modal>
  );
}
