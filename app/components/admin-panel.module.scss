.admin-panel {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--white);
}

.admin-panel-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow-y: auto;
}

.admin-panel-header {
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--black);
    display: flex;
    align-items: center;
    gap: 8px;
  }

  p {
    margin: 0;
    color: var(--black);
    opacity: 0.7;
    font-size: 14px;
  }
}

.admin-panel-body {
  flex: 1;
  display: flex;
  flex-direction: column;
}

// 统计卡片网格
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
  }
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary);
  border-radius: 12px;
  color: white;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 28px;
  font-weight: 700;
  color: var(--black);
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
  font-weight: 500;
}

// 开发中提示
.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  text-align: center;
  color: var(--black);
}

.coming-soon-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.coming-soon-text {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--black);
}

.coming-soon-desc {
  font-size: 16px;
  opacity: 0.7;
  color: var(--black);
}

// 加载状态
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: var(--black);
  opacity: 0.7;
}

// 服务商列表
.provider-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.provider-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
  }
}

.provider-info {
  flex: 1;
}

.provider-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.provider-type {
  font-size: 14px;
  color: var(--primary);
  margin-bottom: 4px;
}

.provider-url {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
}

.provider-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

// 用户列表
.user-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.user-card {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
  }
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
  margin-bottom: 4px;
}

.user-role {
  font-size: 12px;
  color: var(--primary);
  margin-bottom: 4px;
}

.user-quota {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
}

.user-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

// 配置列表
.config-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-item {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--card-shadow);
}

.config-info {
  flex: 1;
}

.config-key {
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.config-desc {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
}

.config-value {
  display: flex;
  align-items: center;
}

// 日志列表
.log-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.log-item {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--card-shadow);
}

.log-info {
  flex: 1;
}

.log-model {
  font-size: 16px;
  font-weight: 600;
  color: var(--black);
  margin-bottom: 4px;
}

.log-provider {
  font-size: 14px;
  color: var(--primary);
  margin-bottom: 4px;
}

.log-time {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
}

.log-details {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.log-tokens {
  font-size: 12px;
  color: var(--black);
  opacity: 0.7;
}

.log-status {
  font-size: 14px;
  font-weight: 600;
}

// 分页
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 20px 0;
}

.page-info {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
}

// 响应式设计
@media (max-width: 768px) {
  .admin-panel-content {
    padding: 16px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 16px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 24px;
  }

  .stat-number {
    font-size: 24px;
  }

  .admin-panel-header h2 {
    font-size: 20px;
  }

  .provider-card,
  .user-card,
  .config-item,
  .log-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .provider-actions,
  .user-actions {
    width: 100%;
    justify-content: flex-end;
  }
}
