import prisma from './prisma';
import { decrypt } from './encryption';
import { ProviderType } from '../generated/prisma';

export interface ProviderConfig {
  id: string;
  name: string;
  type: ProviderType;
  baseUrl: string;
  apiKey: string;
  apiVersion?: string;
  modelsConfig?: any;
  extraConfig?: any;
  enabled: boolean;
}

/**
 * 获取所有启用的服务商配置
 */
export async function getEnabledProviders(): Promise<ProviderConfig[]> {
  const providers = await prisma.provider.findMany({
    where: { enabled: true },
    orderBy: { sortOrder: 'asc' },
  });

  return providers.map(provider => ({
    id: provider.id,
    name: provider.name,
    type: provider.providerType,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    apiVersion: provider.apiVersion || undefined,
    modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
    extraConfig: provider.extraConfig ? JSON.parse(provider.extraConfig) : null,
    enabled: provider.enabled,
  }));
}

/**
 * 根据服务商类型获取配置
 */
export async function getProviderByType(type: ProviderType): Promise<ProviderConfig | null> {
  const provider = await prisma.provider.findFirst({
    where: { 
      providerType: type,
      enabled: true 
    },
    orderBy: { sortOrder: 'asc' },
  });

  if (!provider) return null;

  return {
    id: provider.id,
    name: provider.name,
    type: provider.providerType,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    apiVersion: provider.apiVersion || undefined,
    modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
    extraConfig: provider.extraConfig ? JSON.parse(provider.extraConfig) : null,
    enabled: provider.enabled,
  };
}

/**
 * 根据ID获取服务商配置
 */
export async function getProviderById(id: string): Promise<ProviderConfig | null> {
  const provider = await prisma.provider.findUnique({
    where: { id },
  });

  if (!provider || !provider.enabled) return null;

  return {
    id: provider.id,
    name: provider.name,
    type: provider.providerType,
    baseUrl: provider.baseUrl,
    apiKey: decrypt(provider.apiKey),
    apiVersion: provider.apiVersion || undefined,
    modelsConfig: provider.modelsConfig ? JSON.parse(provider.modelsConfig) : null,
    extraConfig: provider.extraConfig ? JSON.parse(provider.extraConfig) : null,
    enabled: provider.enabled,
  };
}

/**
 * 记录API调用日志
 */
export async function logApiCall(data: {
  userId?: number;
  providerId?: string;
  modelName: string;
  requestTokens?: number;
  responseTokens?: number;
  totalTokens?: number;
  cost?: number;
  status: 'success' | 'error';
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
}) {
  try {
    // 检查 providerId 是否存在于数据库中
    let validProviderId = null;
    if (data.providerId) {
      const provider = await prisma.provider.findUnique({
        where: { id: data.providerId },
      });
      if (provider) {
        validProviderId = data.providerId;
      }
    }

    await prisma.apiLog.create({
      data: {
        userId: data.userId,
        providerId: validProviderId,
        modelName: data.modelName,
        requestTokens: data.requestTokens || 0,
        responseTokens: data.responseTokens || 0,
        totalTokens: data.totalTokens || 0,
        cost: data.cost || 0,
        status: data.status,
        errorMessage: data.errorMessage,
        ipAddress: data.ipAddress,
        userAgent: data.userAgent,
      },
    });
  } catch (error) {
    console.error('Failed to log API call:', error);
  }
}

/**
 * 获取系统配置
 */
export async function getSystemConfig(key: string): Promise<string | null> {
  const config = await prisma.systemConfig.findUnique({
    where: { configKey: key },
  });
  return config?.configValue || null;
}

/**
 * 检查是否禁用前端配置
 */
export async function isFrontendConfigDisabled(): Promise<boolean> {
  const config = await getSystemConfig('disable_frontend_config');
  return config === 'true';
}
