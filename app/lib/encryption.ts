// 简化版加密函数，使用 Base64 编码（仅用于演示，生产环境应使用更安全的方法）
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'default-secret-key';

/**
 * 简单的 Base64 编码（仅用于演示）
 * 生产环境应该使用更安全的加密方法
 */
export function encrypt(text: string): string {
  try {
    // 使用简单的 Base64 编码，避免非ASCII字符问题
    // 在生产环境中应该使用 AES 等更安全的加密方法
    const key = ENCRYPTION_KEY;

    // 将文本转换为字节数组
    const textBytes = Buffer.from(text, 'utf8');
    const keyBytes = Buffer.from(key, 'utf8');

    // 简单的异或加密，但确保结果是安全的
    const encrypted = Buffer.alloc(textBytes.length);
    for (let i = 0; i < textBytes.length; i++) {
      encrypted[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
    }

    // 转换为 Base64，确保结果是ASCII兼容的
    return encrypted.toString('base64');
  } catch (error) {
    console.error('Encryption error:', error);
    return Buffer.from(text, 'utf8').toString('base64'); // 降级到简单 Base64
  }
}

/**
 * 简单的 Base64 解码
 */
export function decrypt(encryptedText: string): string {
  try {
    const key = ENCRYPTION_KEY;

    // 从 Base64 解码
    const encryptedBytes = Buffer.from(encryptedText, 'base64');
    const keyBytes = Buffer.from(key, 'utf8');

    // 异或解密
    const decrypted = Buffer.alloc(encryptedBytes.length);
    for (let i = 0; i < encryptedBytes.length; i++) {
      decrypted[i] = encryptedBytes[i] ^ keyBytes[i % keyBytes.length];
    }

    // 转换回字符串
    return decrypted.toString('utf8');
  } catch (error) {
    console.error('Decryption error:', error);
    return Buffer.from(encryptedText, 'base64').toString('utf8'); // 降级到简单 Base64
  }
}

/**
 * 生成随机密钥
 */
export function generateKey(): string {
  // 使用 Web Crypto API 或简单的随机字符串生成
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 32; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}
