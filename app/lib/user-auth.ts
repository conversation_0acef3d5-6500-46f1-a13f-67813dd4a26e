import { NextRequest } from "next/server";
import jwt from "jsonwebtoken";

const JWT_SECRET = process.env.JWT_SECRET || "your-jwt-secret-key";

export interface UserTokenData {
  userId: number;
  username: string;
  role: "admin" | "user";
  timestamp: number;
}

// 验证用户token
export function verifyUserToken(req: NextRequest): UserTokenData | null {
  try {
    const authHeader = req.headers.get("Authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return null;
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, JWT_SECRET) as UserTokenData;
    
    return decoded;
  } catch (error) {
    return null;
  }
}

// 验证管理员权限
export function verifyAdminUser(req: NextRequest): UserTokenData | null {
  const tokenData = verifyUserToken(req);
  if (!tokenData || tokenData.role !== "admin") {
    return null;
  }
  return tokenData;
}

// 生成用户token
export function generateUserToken(userData: {
  userId: number;
  username: string;
  role: "admin" | "user";
}): string {
  return jwt.sign(
    {
      ...userData,
      timestamp: Date.now(),
    },
    JWT_SECRET,
    { expiresIn: "7d" }
  );
}
