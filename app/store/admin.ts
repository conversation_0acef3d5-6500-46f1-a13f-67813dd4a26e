import { StoreKey } from "../constant";
import { createPersistStore } from "../utils/store";

export type AdminPanelTab = 
  | "dashboard" 
  | "providers" 
  | "users" 
  | "config" 
  | "logs";

export interface AdminState {
  isAdminMode: boolean;
  activeTab: AdminPanelTab;
}

const DEFAULT_ADMIN_STATE: AdminState = {
  isAdminMode: false,
  activeTab: "dashboard",
};

export const useAdminStore = createPersistStore(
  { ...DEFAULT_ADMIN_STATE },
  (set, get) => ({
    enterAdminMode() {
      set(() => ({
        isAdminMode: true,
        activeTab: "dashboard",
      }));
    },

    exitAdminMode() {
      set(() => ({
        isAdminMode: false,
        activeTab: "dashboard",
      }));
    },

    setActiveTab(tab: AdminPanelTab) {
      set(() => ({
        activeTab: tab,
      }));
    },
  }),
  {
    name: StoreKey.Admin,
    version: 1,
  }
);
