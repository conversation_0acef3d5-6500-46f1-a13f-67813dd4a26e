import { <PERSON><PERSON>ey } from "../constant";
import { createPersistStore } from "../utils/store";

export interface UserInfo {
  id: number;
  username: string;
  email?: string;
  role: "admin" | "user";
  apiQuota: number;
  usedQuota: number;
}

export interface UserState {
  isLoggedIn: boolean;
  userInfo: UserInfo | null;
  token: string | null;
}

const DEFAULT_USER_STATE: UserState = {
  isLoggedIn: false,
  userInfo: null,
  token: null,
};

export const useUserStore = createPersistStore(
  { ...DEFAULT_USER_STATE },
  (set, get) => ({
    login(token: string, userInfo: UserInfo) {
      set(() => ({
        isLoggedIn: true,
        token,
        userInfo,
      }));
    },

    logout() {
      set(() => ({
        isLoggedIn: false,
        token: null,
        userInfo: null,
      }));
    },

    updateUserInfo(userInfo: Partial<UserInfo>) {
      const currentUser = get().userInfo;
      if (currentUser) {
        set(() => ({
          userInfo: { ...currentUser, ...userInfo },
        }));
      }
    },

    isAdmin() {
      return get().userInfo?.role === "admin";
    },

    async fetchUserInfo() {
      const state = get();
      const token = state.token;
      if (!token) return;

      try {
        const response = await fetch("/api/auth/me", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            set(() => ({
              userInfo: data.user,
            }));
          }
        } else {
          // Token 无效，清除登录状态
          set(() => ({
            isLoggedIn: false,
            token: null,
            userInfo: null,
          }));
        }
      } catch (error) {
        console.error("Failed to fetch user info:", error);
      }
    },
  }),
  {
    name: StoreKey.User,
    version: 1,
  }
);
