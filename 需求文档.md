## 商业化 NextChat 后端中转一体化改造需求文档（概要版）

### 1. 项目目标
将 NextChat 由前端直连第三方服务商的模式，升级为前后端一体化、所有敏感信息后端中转、支持多用户和商业化运营的架构。

---

### 2. 架构核心

- **技术栈**：Next.js + React + TypeScript + Antd + 状态管理（如 Zustand）+ MySQL 数据库 + ORM（如 Prisma）
- **部署方式**：Next.js 一体式服务，既负责页面渲染，也负责 API 路由转发
- **安全原则**：API Key、服务商 URL 等敏感信息只在服务器端处理，前端用户不可见

---

### 3. 主要功能与改造点

#### 3.1. API 层中转
- 所有跟 AI 服务商（如 OpenAI、智谱等）交互的操作，统一通过 Next.js 的 API 路由实现后端转发
- 前端发起聊天、模型选择等请求时，全部请求自己的后端 API，由后端获取配置后再请求第三方
- 前端永远无法获得真实 API Key 和服务商地址

#### 3.2. 数据库配置
- 后端通过数据库集中管理渠道商信息、系统参数、用户数据等
- 数据库表结构可根据实际业务灵活设计，满足渠道配置、用户管理、运营需求等

#### 3.3. 管理后台
- 实现一个简洁高效的管理界面，支持渠道商配置、系统参数控制、用户管理（可留作后续扩展）
- 管理员通过后台界面统一维护和调整所有服务商配置

#### 3.4. 用户体系与权限
- 初期可支持简单的访客或硬编码管理员鉴权
- 预留后续扩展注册、登录、权限控制的空间

#### 3.5. 计费与用量统计（可选/后续）
- 后端对每次请求进行用量统计，为后续计费、风控、运营提供基础

---

### 4. 实现原则

- 前端全部请求通过自有 API 层中转，杜绝任何敏感数据直达前端
- 管理后台、用户体系、计费等功能分步实现，逐渐完善
- 具体数据库表结构、API 路由、业务逻辑根据实际需求灵活设计

---

### 5. 预期效果

- **安全**：API Key 完全后端隔离，避免泄漏
- **易用**：普通用户无需繁琐配置，开箱即用
- **可运营**：支持多渠道管理、用户分级、用量统计等商业化需求
- **可扩展**：便于后续接入新的 AI 服务商、接入计费、权限、监控等功能

