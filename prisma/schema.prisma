// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../app/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 服务商配置表
model Provider {
  id            String   @id
  name          String
  providerType  ProviderType @map("provider_type")
  baseUrl       String   @map("base_url")
  apiKey        String   @map("api_key")
  apiVersion    String?  @map("api_version")
  modelsConfig  String?  @map("models_config") // SQLite 使用 String 存储 JSON
  extraConfig   String?  @map("extra_config")  // SQLite 使用 String 存储 JSON
  enabled       Boolean  @default(true)
  sortOrder     Int      @default(0) @map("sort_order")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // 关联的API调用日志
  apiLogs       ApiLog[]

  @@index([providerType], map: "idx_provider_type")
  @@index([enabled], map: "idx_enabled")
  @@map("providers")
}

// 服务商类型枚举
enum ProviderType {
  openai
  azure
  google
  anthropic
  bytedance
  alibaba
  moonshot
  deepseek
  xai
  siliconflow
  custom
}

// 系统配置表
model SystemConfig {
  configKey     String      @id @map("config_key")
  configValue   String      @map("config_value")
  configType    ConfigType  @default(string) @map("config_type")
  description   String?
  isPublic      Boolean     @default(false) @map("is_public")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")

  @@map("system_config")
}

// 配置类型枚举
enum ConfigType {
  string
  number
  boolean
  json
}

// 用户表
model User {
  id            Int         @id @default(autoincrement())
  username      String      @unique
  email         String?     @unique
  passwordHash  String      @map("password_hash")
  role          UserRole    @default(user)
  status        UserStatus  @default(active)
  apiQuota      Int         @default(-1) @map("api_quota")
  usedQuota     Int         @default(0) @map("used_quota")
  lastLoginAt   DateTime?   @map("last_login_at")
  createdAt     DateTime    @default(now()) @map("created_at")
  updatedAt     DateTime    @updatedAt @map("updated_at")

  // 关联的API调用日志
  apiLogs       ApiLog[]

  @@index([username], map: "idx_username")
  @@index([email], map: "idx_email")
  @@index([role], map: "idx_role")
  @@map("users")
}

// 用户角色枚举
enum UserRole {
  admin
  user
}

// 用户状态枚举
enum UserStatus {
  active
  disabled
}

// API调用日志表
model ApiLog {
  id             Int         @id @default(autoincrement())
  userId         Int?        @map("user_id")
  providerId     String?     @map("provider_id")
  modelName      String      @map("model_name")
  requestTokens  Int         @default(0) @map("request_tokens")
  responseTokens Int         @default(0) @map("response_tokens")
  totalTokens    Int         @default(0) @map("total_tokens")
  cost           Float       @default(0.0)
  status         LogStatus   @default(success)
  errorMessage   String?     @map("error_message")
  ipAddress      String?     @map("ip_address")
  userAgent      String?     @map("user_agent")
  createdAt      DateTime    @default(now()) @map("created_at")

  // 外键关联
  user           User?       @relation(fields: [userId], references: [id], onDelete: SetNull)
  provider       Provider?   @relation(fields: [providerId], references: [id], onDelete: SetNull)

  @@index([userId], map: "idx_user_id")
  @@index([providerId], map: "idx_provider_id")
  @@index([createdAt], map: "idx_created_at")
  @@map("api_logs")
}

// 日志状态枚举
enum LogStatus {
  success
  error
}
