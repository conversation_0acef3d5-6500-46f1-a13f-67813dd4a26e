# QADChat 商业化改造数据库设计

## 当前架构分析

### 现有问题
1. **安全风险**: API Key 存储在前端 localStorage/IndexedDB 中，用户可见
2. **配置分散**: 每个用户需要自行配置各服务商的 API Key 和端点
3. **无法统一管理**: 管理员无法集中管理和控制服务商配置
4. **无用户体系**: 缺乏用户认证和权限控制

### 当前技术栈
- **前端**: Next.js + React + TypeScript + Antd + Zustand
- **存储**: IndexedDB (前端本地存储)
- **API路由**: Next.js API Routes 作为代理层
- **认证**: 简单的 API Key 验证

## 数据库表设计

### 1. 服务商配置表 (providers)
```sql
CREATE TABLE providers (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '显示名称',
  provider_type ENUM('openai', 'azure', 'google', 'anthropic', 'bytedance', 'alibaba', 'moonshot', 'deepseek', 'xai', 'siliconflow', 'custom') NOT NULL,
  base_url VARCHAR(500) NOT NULL COMMENT 'API 基础地址',
  api_key TEXT NOT NULL COMMENT 'API 密钥 (加密存储)',
  api_version VARCHAR(50) DEFAULT NULL COMMENT 'API 版本',
  models_config JSON DEFAULT NULL COMMENT '支持的模型配置',
  extra_config JSON DEFAULT NULL COMMENT '额外配置参数',
  enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  sort_order INT DEFAULT 0 COMMENT '排序',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_provider_type (provider_type),
  INDEX idx_enabled (enabled)
);
```

### 2. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
  config_key VARCHAR(100) PRIMARY KEY,
  config_value TEXT NOT NULL COMMENT '配置值',
  config_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
  description VARCHAR(500) DEFAULT NULL COMMENT '配置说明',
  is_public BOOLEAN DEFAULT FALSE COMMENT '是否对前端公开',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 3. 用户表 (users) - 预留
```sql
CREATE TABLE users (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE DEFAULT NULL,
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  role ENUM('admin', 'user') DEFAULT 'user',
  status ENUM('active', 'disabled') DEFAULT 'active',
  api_quota INT DEFAULT -1 COMMENT 'API调用配额 (-1表示无限制)',
  used_quota INT DEFAULT 0 COMMENT '已使用配额',
  last_login_at TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_role (role)
);
```

### 4. API调用日志表 (api_logs) - 预留
```sql
CREATE TABLE api_logs (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  user_id BIGINT DEFAULT NULL,
  provider_id VARCHAR(50) NOT NULL,
  model_name VARCHAR(100) NOT NULL,
  request_tokens INT DEFAULT 0,
  response_tokens INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  cost DECIMAL(10,6) DEFAULT 0.000000 COMMENT '成本',
  status ENUM('success', 'error') DEFAULT 'success',
  error_message TEXT DEFAULT NULL,
  ip_address VARCHAR(45) DEFAULT NULL,
  user_agent TEXT DEFAULT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  INDEX idx_user_id (user_id),
  INDEX idx_provider_id (provider_id),
  INDEX idx_created_at (created_at),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (provider_id) REFERENCES providers(id) ON DELETE CASCADE
);
```

## 初始数据

### 系统配置初始数据
```sql
INSERT INTO system_config (config_key, config_value, config_type, description, is_public) VALUES
('admin_password', 'admin123', 'string', '管理员密码', FALSE),
('disable_frontend_config', 'false', 'boolean', '是否禁用前端配置界面', TRUE),
('default_model', 'gpt-4o-mini', 'string', '默认模型', TRUE),
('max_tokens_per_request', '4000', 'number', '单次请求最大token数', TRUE),
('enable_user_registration', 'false', 'boolean', '是否开放用户注册', TRUE);
```

### 默认管理员用户
```sql
INSERT INTO users (username, email, password_hash, role) VALUES
('admin', '<EMAIL>', '$2b$10$...', 'admin');
```

## API 接口设计

### 管理员接口
- `POST /api/admin/auth` - 管理员登录
- `GET/POST /api/admin/providers` - 服务商管理
- `GET/POST /api/admin/config` - 系统配置管理
- `GET /api/admin/logs` - 查看API调用日志

### 前端接口
- `GET /api/providers` - 获取可用服务商列表
- `GET /api/config` - 获取公开系统配置
- `POST /api/chat` - 统一聊天接口 (替代现有的分散接口)

## 安全考虑

1. **API Key 加密**: 使用 AES 加密存储 API Key
2. **访问控制**: 管理员接口需要认证
3. **日志记录**: 记录所有 API 调用用于审计
4. **配额控制**: 支持用户级别的配额限制

## 迁移策略

1. **第一阶段**: 数据库集成，保持现有前端配置功能
2. **第二阶段**: 管理后台开发，支持后台配置
3. **第三阶段**: 前端改造，移除配置界面
4. **第四阶段**: 用户体系完善
