# QADChat 商业化版本部署指南

## 概述

QADChat 商业化版本是一个基于 Next.js 的全栈应用，集成了数据库存储、用户管理、服务商配置等功能。本文档将指导您完成部署过程。

## 系统要求

- Node.js 18+ 
- MySQL 8.0+ 或 SQLite（开发环境）
- 内存：至少 2GB RAM
- 存储：至少 10GB 可用空间

## 环境变量配置

创建 `.env` 文件并配置以下环境变量：

```bash
# 数据库配置
# SQLite（开发环境）
DATABASE_URL="file:./dev.db"

# MySQL（生产环境）
# DATABASE_URL="mysql://username:password@localhost:3306/qadchat"

# JWT 密钥（用于用户认证）
JWT_SECRET="your-super-secret-jwt-key-here"

# 加密密钥（用于加密API Key）
ENCRYPTION_KEY="your-32-character-encryption-key"

# 管理员密码
ADMIN_PASSWORD="QQwoxiang1!"

# Next.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"
```

## 安装步骤

### 1. 克隆项目

```bash
git clone <repository-url>
cd QADChatPro
```

### 2. 安装依赖

```bash
yarn install
```

### 3. 数据库设置

#### 开发环境（SQLite）

```bash
# 生成 Prisma 客户端
yarn db:generate

# 运行数据库迁移
yarn db:migrate

# 初始化数据库数据
yarn db:init
```

#### 生产环境（MySQL）

1. 创建 MySQL 数据库：
```sql
CREATE DATABASE qadchat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改 `.env` 文件中的 `DATABASE_URL`

3. 运行迁移：
```bash
yarn db:push
yarn db:init
```

### 4. 构建应用

```bash
yarn build
```

### 5. 启动应用

```bash
# 开发环境
yarn dev

# 生产环境
yarn start
```

## 生产环境部署

### 使用 PM2 部署

1. 安装 PM2：
```bash
npm install -g pm2
```

2. 创建 PM2 配置文件 `ecosystem.config.js`：
```javascript
module.exports = {
  apps: [{
    name: 'qadchat',
    script: 'yarn',
    args: 'start',
    cwd: '/path/to/your/app',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    }
  }]
}
```

3. 启动应用：
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### 使用 Docker 部署

1. 创建 `Dockerfile`：
```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package.json yarn.lock ./
RUN yarn install --frozen-lockfile

COPY . .
RUN yarn build

EXPOSE 3000

CMD ["yarn", "start"]
```

2. 创建 `docker-compose.yml`：
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=mysql://root:password@db:3306/qadchat
      - JWT_SECRET=your-jwt-secret
      - ENCRYPTION_KEY=your-encryption-key
    depends_on:
      - db
  
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=qadchat
    volumes:
      - mysql_data:/var/lib/mysql

volumes:
  mysql_data:
```

3. 启动服务：
```bash
docker-compose up -d
```

## 初始配置

### 1. 管理员登录

访问 `http://your-domain/admin`，使用配置的管理员密码登录。

### 2. 配置服务商

在管理后台的"服务商管理"页面添加 AI 服务商：

- **OpenAI**: 配置 API Key 和 Base URL
- **Anthropic**: 配置 API Key
- **Google**: 配置 API Key
- 其他服务商...

### 3. 系统配置

在"系统配置"页面调整：

- `disable_frontend_config`: 设为 `true` 禁用前端配置界面
- `default_model`: 设置默认模型
- `enable_user_registration`: 是否开放用户注册

## 安全建议

1. **更改默认密码**: 部署后立即更改管理员密码
2. **使用 HTTPS**: 生产环境必须使用 HTTPS
3. **定期备份**: 定期备份数据库
4. **监控日志**: 监控应用日志和错误
5. **更新依赖**: 定期更新依赖包

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `DATABASE_URL` 配置
   - 确认数据库服务正在运行
   - 检查网络连接

2. **构建失败**
   - 清除缓存：`yarn cache clean`
   - 删除 `node_modules` 重新安装
   - 检查 Node.js 版本

3. **API 调用失败**
   - 检查服务商 API Key 配置
   - 确认网络可以访问第三方服务
   - 查看应用日志

### 日志查看

```bash
# PM2 日志
pm2 logs qadchat

# Docker 日志
docker-compose logs -f app
```

## 维护

### 数据库备份

```bash
# MySQL 备份
mysqldump -u username -p qadchat > backup.sql

# SQLite 备份
cp dev.db backup.db
```

### 更新应用

```bash
git pull
yarn install
yarn build
pm2 restart qadchat
```

## 支持

如遇到问题，请查看：
1. 应用日志
2. 数据库日志
3. 系统资源使用情况

或联系技术支持。
