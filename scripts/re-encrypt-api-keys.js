const { PrismaClient } = require('../app/generated/prisma');

const prisma = new PrismaClient();

// 新的加密函数（与修复后的 app/lib/encryption.ts 保持一致）
function encrypt(text) {
  try {
    const key = process.env.ENCRYPTION_KEY || 'default-secret-key';
    
    // 将文本转换为字节数组
    const textBytes = Buffer.from(text, 'utf8');
    const keyBytes = Buffer.from(key, 'utf8');
    
    // 简单的异或加密，但确保结果是安全的
    const encrypted = Buffer.alloc(textBytes.length);
    for (let i = 0; i < textBytes.length; i++) {
      encrypted[i] = textBytes[i] ^ keyBytes[i % keyBytes.length];
    }
    
    // 转换为 Base64，确保结果是ASCII兼容的
    return encrypted.toString('base64');
  } catch (error) {
    console.error('Encryption error:', error);
    return Buffer.from(text, 'utf8').toString('base64'); // 降级到简单 Base64
  }
}

async function reEncryptApiKeys() {
  try {
    console.log('Re-encrypting API keys with fixed encryption...');

    // 使用测试用的 API Key 重新加密
    const testApiKeys = {
      'openai_test': 'sk-test-fake-openai-key-for-testing-only',
      'anthropic_test': 'sk-ant-test-fake-anthropic-key-for-testing',
      'google_test': 'AIza-test-fake-google-key-for-testing-only'
    };

    for (const [providerId, apiKey] of Object.entries(testApiKeys)) {
      const encryptedKey = encrypt(apiKey);
      
      await prisma.provider.update({
        where: { id: providerId },
        data: { apiKey: encryptedKey },
      });
      
      console.log(`✅ Re-encrypted API key for ${providerId}`);
      console.log(`   Original: ${apiKey.substring(0, 15)}...`);
      console.log(`   Encrypted: ${encryptedKey.substring(0, 20)}...`);
    }

    console.log('\n✅ All API keys re-encrypted successfully!');
    console.log('⚠️  请注意：这些是测试用的假API Key，请在生产环境中使用真实的API Key');

  } catch (error) {
    console.error('❌ Error re-encrypting API keys:', error);
  } finally {
    await prisma.$disconnect();
  }
}

reEncryptApiKeys();
