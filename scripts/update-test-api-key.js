const { PrismaClient } = require('../app/generated/prisma');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 简单的加密函数（与 app/lib/encryption.ts 保持一致）
function encrypt(text) {
  const algorithm = 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

async function updateTestApiKey() {
  try {
    console.log('Updating test API key...');

    // 这里使用一个测试用的假API Key
    // 在实际使用时，请替换为真实的API Key
    const testApiKey = 'sk-test-fake-api-key-for-testing-only';

    await prisma.provider.update({
      where: { id: 'openai_test' },
      data: {
        apiKey: encrypt(testApiKey),
      },
    });

    console.log('✅ Test API key updated');
    console.log('⚠️  注意：这是一个测试用的假API Key，请在生产环境中使用真实的API Key');

  } catch (error) {
    console.error('❌ Error updating test API key:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateTestApiKey();
