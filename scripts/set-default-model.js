const { PrismaClient } = require('../app/generated/prisma');

const prisma = new PrismaClient();

async function setDefaultModel() {
  try {
    console.log('Setting default model...');

    // 设置默认模型为 gpt-4.1
    await prisma.systemConfig.upsert({
      where: { configKey: 'default_model' },
      update: { configValue: 'gpt-4.1' },
      create: {
        configKey: 'default_model',
        configValue: 'gpt-4.1',
        configType: 'string',
        description: '系统默认模型',
        isPublic: true,
      },
    });

    console.log('✅ Default model set to gpt-4.1');

  } catch (error) {
    console.error('❌ Error setting default model:', error);
  } finally {
    await prisma.$disconnect();
  }
}

setDefaultModel();
