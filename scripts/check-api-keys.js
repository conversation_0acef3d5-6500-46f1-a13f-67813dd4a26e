const { PrismaClient } = require('../app/generated/prisma');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 简单的解密函数（与 app/lib/encryption.ts 保持一致）
function decrypt(encryptedText) {
  try {
    const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
    const decoded = Buffer.from(encryptedText, 'base64').toString();
    let decrypted = '';
    for (let i = 0; i < decoded.length; i++) {
      decrypted += String.fromCharCode(
        decoded.charCodeAt(i) ^ key.charCodeAt(i % key.length)
      );
    }
    return decrypted;
  } catch (error) {
    console.error('Decryption error:', error);
    return Buffer.from(encryptedText, 'base64').toString(); // 降级到简单 Base64
  }
}

async function checkApiKeys() {
  try {
    console.log('Checking API keys in database...');

    const providers = await prisma.provider.findMany();

    providers.forEach((provider, index) => {
      console.log(`\n${index + 1}. Provider: ${provider.name} (${provider.providerType})`);
      console.log(`   ID: ${provider.id}`);
      console.log(`   Encrypted API Key: ${provider.apiKey.substring(0, 20)}...`);
      
      try {
        const decryptedKey = decrypt(provider.apiKey);
        console.log(`   Decrypted API Key: ${decryptedKey.substring(0, 10)}...`);
        
        // 检查是否包含非ASCII字符
        const hasNonAscii = /[^\x00-\x7F]/.test(decryptedKey);
        if (hasNonAscii) {
          console.log(`   ⚠️  WARNING: API Key contains non-ASCII characters!`);
        } else {
          console.log(`   ✅ API Key is ASCII-compatible`);
        }
      } catch (error) {
        console.log(`   ❌ Failed to decrypt: ${error.message}`);
      }
    });

  } catch (error) {
    console.error('❌ Error checking API keys:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkApiKeys();
