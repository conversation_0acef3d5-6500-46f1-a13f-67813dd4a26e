import { PrismaClient } from '../app/generated/prisma';
import { encrypt } from '../app/lib/encryption';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

async function main() {
  console.log('开始初始化数据库...');

  // 初始化系统配置
  const systemConfigs = [
    {
      configKey: 'admin_password',
      configValue: process.env.ADMIN_PASSWORD || 'admin123',
      configType: 'string' as const,
      description: '管理员密码',
      isPublic: false,
    },
    {
      configKey: 'disable_frontend_config',
      configValue: 'false',
      configType: 'boolean' as const,
      description: '是否禁用前端配置界面',
      isPublic: true,
    },
    {
      configKey: 'default_model',
      configValue: 'gpt-4o-mini',
      configType: 'string' as const,
      description: '默认模型',
      isPublic: true,
    },
    {
      configKey: 'max_tokens_per_request',
      configValue: '4000',
      configType: 'number' as const,
      description: '单次请求最大token数',
      isPublic: true,
    },
    {
      configKey: 'enable_user_registration',
      configValue: 'false',
      configType: 'boolean' as const,
      description: '是否开放用户注册',
      isPublic: true,
    },
  ];

  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: { configKey: config.configKey },
      update: config,
      create: config,
    });
  }

  // 创建默认管理员用户
  const adminPassword = await bcrypt.hash('admin123', 10);
  await prisma.user.upsert({
    where: { username: 'admin' },
    update: {
      passwordHash: adminPassword,
      role: 'admin',
    },
    create: {
      username: 'admin',
      email: '<EMAIL>',
      passwordHash: adminPassword,
      role: 'admin',
    },
  });

  // 创建示例服务商配置 (可选)
  const sampleProviders = [
    {
      id: 'openai_default',
      name: 'OpenAI 官方',
      providerType: 'openai' as const,
      baseUrl: 'https://api.openai.com',
      apiKey: encrypt('your-openai-api-key-here'),
      enabled: false, // 默认禁用，需要管理员配置真实的API Key
      sortOrder: 1,
    },
    {
      id: 'anthropic_default',
      name: 'Anthropic 官方',
      providerType: 'anthropic' as const,
      baseUrl: 'https://api.anthropic.com',
      apiKey: encrypt('your-anthropic-api-key-here'),
      enabled: false,
      sortOrder: 2,
    },
  ];

  for (const provider of sampleProviders) {
    await prisma.provider.upsert({
      where: { id: provider.id },
      update: provider,
      create: provider,
    });
  }

  console.log('数据库初始化完成！');
  console.log('默认管理员账号: admin / admin123');
}

main()
  .catch((e) => {
    console.error('数据库初始化失败:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
