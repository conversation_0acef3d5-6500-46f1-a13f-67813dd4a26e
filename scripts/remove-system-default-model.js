const { PrismaClient } = require('../app/generated/prisma');

const prisma = new PrismaClient();

async function removeSystemDefaultModel() {
  try {
    console.log('Removing system default model config...');

    // 删除系统配置中的默认模型设置
    await prisma.systemConfig.deleteMany({
      where: { configKey: 'default_model' },
    });

    console.log('✅ System default model config removed');
    console.log('现在系统将使用用户自己配置的默认模型');

  } catch (error) {
    console.error('❌ Error removing system default model:', error);
  } finally {
    await prisma.$disconnect();
  }
}

removeSystemDefaultModel();
