const { PrismaClient } = require('../app/generated/prisma');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 简单的加密函数（与 app/lib/encryption.ts 保持一致）
function encrypt(text) {
  const algorithm = 'aes-256-cbc';
  const key = process.env.ENCRYPTION_KEY || 'default-encryption-key-32-chars!!';
  const iv = crypto.randomBytes(16);
  
  const cipher = crypto.createCipher(algorithm, key);
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  return iv.toString('hex') + ':' + encrypted;
}

async function addTestProviders() {
  try {
    console.log('Adding test providers...');

    // 添加 OpenAI 测试配置
    await prisma.provider.upsert({
      where: { id: 'openai_test' },
      update: {},
      create: {
        id: 'openai_test',
        name: 'OpenAI Test',
        providerType: 'openai',
        baseUrl: 'https://api.openai.com/v1',
        apiKey: encrypt('your-openai-api-key-here'), // 请替换为真实的API Key
        enabled: true,
        sortOrder: 1,
      },
    });

    // 添加 Anthropic 测试配置
    await prisma.provider.upsert({
      where: { id: 'anthropic_test' },
      update: {},
      create: {
        id: 'anthropic_test',
        name: 'Anthropic Test',
        providerType: 'anthropic',
        baseUrl: 'https://api.anthropic.com',
        apiKey: encrypt('your-anthropic-api-key-here'), // 请替换为真实的API Key
        apiVersion: '2023-06-01',
        enabled: true,
        sortOrder: 2,
      },
    });

    // 添加 Google 测试配置
    await prisma.provider.upsert({
      where: { id: 'google_test' },
      update: {},
      create: {
        id: 'google_test',
        name: 'Google Test',
        providerType: 'google',
        baseUrl: 'https://generativelanguage.googleapis.com',
        apiKey: encrypt('your-google-api-key-here'), // 请替换为真实的API Key
        enabled: true,
        sortOrder: 3,
      },
    });

    console.log('✅ Test providers added successfully!');
    console.log('');
    console.log('⚠️  请注意：');
    console.log('1. 请在数据库中更新真实的 API Key');
    console.log('2. 或者通过管理面板配置服务商');
    console.log('3. 现在可以测试聊天功能了');

  } catch (error) {
    console.error('❌ Error adding test providers:', error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestProviders();
