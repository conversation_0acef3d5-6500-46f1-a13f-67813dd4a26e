# QADChat Pro - 商业化 AI 聊天应用

基于 NextChat 的企业级 AI 聊天应用，支持多用户、统一管理、后端中转等商业化功能。

## ✨ 核心特性

### 🏢 商业化功能
- **后端中转架构** - 所有 API 请求通过后端转发，保护敏感信息
- **统一服务商管理** - 管理员集中配置所有 AI 服务商，用户开箱即用
- **用户体系** - 支持用户注册、登录、权限控制和配额管理
- **管理后台** - 完整的管理界面，支持服务商、用户、系统配置管理
- **API 调用统计** - 详细的调用日志和统计分析

### 🚀 技术特性
- **现代技术栈** - Next.js 14 + React + TypeScript + Prisma
- **数据库支持** - MySQL/SQLite，完整的数据持久化
- **安全可靠** - JWT 认证、API Key 加密存储、访问控制
- **响应式设计** - 完美适配桌面和移动设备
- **多语言支持** - 中文、英文等多种语言

## 🤖 支持的 AI 服务商

- **OpenAI** - GPT-3.5, GPT-4, GPT-4o 等
- **Anthropic** - Claude 系列模型
- **Google** - Gemini Pro 系列
- **Azure OpenAI** - 企业级 OpenAI 服务
- **国产大模型**:
  - 字节跳动 (豆包)
  - 阿里云 (通义千问)
  - 月之暗面 (Moonshot)
  - DeepSeek
  - XAI (Grok)
  - SiliconFlow
- **自定义服务商** - 支持添加任意兼容 OpenAI API 的服务

## 🏗️ 架构优势

### 传统模式 vs 商业化模式

**传统模式（前端直连）**:
```
用户浏览器 → 第三方 AI 服务商
```
- ❌ API Key 暴露在前端
- ❌ 每个用户需要自行配置
- ❌ 无法统一管理和控制
- ❌ 无法进行用量统计

**商业化模式（后端中转）**:
```
用户浏览器 → QADChat 后端 → 第三方 AI 服务商
```
- ✅ API Key 安全存储在后端
- ✅ 用户开箱即用，无需配置
- ✅ 管理员统一管理所有服务商
- ✅ 完整的用量统计和控制

## 🚀 快速开始

### 环境要求

- Node.js 18+
- MySQL 8.0+ (生产环境) 或 SQLite (开发环境)
- Yarn 包管理器

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/your-username/QADChatPro.git
cd QADChatPro
```

2. **安装依赖**
```bash
yarn install
```

3. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件，配置数据库连接等信息
```

4. **初始化数据库**
```bash
# 生成 Prisma 客户端
yarn db:generate

# 运行数据库迁移
yarn db:migrate

# 初始化数据
yarn db:init
```

5. **启动应用**
```bash
# 开发模式
yarn dev

# 生产模式
yarn build && yarn start
```

6. **访问应用**
- 用户界面: http://localhost:3000
- 管理后台: http://localhost:3000/admin (密码: admin123)

## 📋 使用指南

### 管理员操作

1. **登录管理后台** - 访问 `/admin` 使用管理员密码登录
2. **配置服务商** - 在"服务商管理"中添加 AI 服务商的 API Key
3. **系统配置** - 设置是否禁用前端配置、默认模型等
4. **用户管理** - 管理用户账号、配额等

### 用户操作

1. **注册/登录** - 如果开启用户注册，可以注册新账号
2. **开始聊天** - 直接使用，无需配置 API Key
3. **选择模型** - 从管理员配置的模型中选择

## 🔧 配置说明

### 环境变量

```bash
# 数据库配置
DATABASE_URL="mysql://username:password@localhost:3306/qadchat"

# JWT 密钥
JWT_SECRET="your-jwt-secret-key"

# 加密密钥
ENCRYPTION_KEY="your-encryption-key"

# 管理员密码
ADMIN_PASSWORD="admin123"
```

### 系统配置

在管理后台可以配置：

- `disable_frontend_config` - 是否禁用前端配置界面
- `default_model` - 默认使用的模型
- `enable_user_registration` - 是否开放用户注册
- `max_tokens_per_request` - 单次请求最大 token 数

## 📊 功能特性

### 管理后台功能

- **仪表板** - 系统统计、调用量分析
- **服务商管理** - 添加、编辑、删除 AI 服务商配置
- **用户管理** - 用户账号管理、配额设置
- **系统配置** - 全局系统参数配置
- **调用日志** - API 调用记录和统计

### 用户功能

- **智能对话** - 支持多种 AI 模型的对话
- **会话管理** - 会话保存、导入导出
- **模型切换** - 在可用模型间自由切换
- **个性化设置** - 主题、语言等个人偏好

## 🚀 部署指南

详细的部署说明请参考 [部署文档](docs/deployment.md)

### Docker 部署

```bash
# 使用 Docker Compose
docker-compose up -d
```

### 手动部署

```bash
# 构建项目
yarn build

# 使用 PM2 启动
pm2 start ecosystem.config.js
```

## 🛠️ 开发

### 项目结构

```
QADChatPro/
├── app/                    # Next.js 应用目录
│   ├── admin/             # 管理后台页面
│   ├── api/               # API 路由
│   │   ├── admin/         # 管理员 API
│   │   ├── auth/          # 用户认证 API
│   │   └── chat/          # 聊天 API
│   ├── components/        # React 组件
│   ├── lib/               # 工具库
│   ├── store/             # 状态管理
│   └── generated/         # Prisma 生成的文件
├── prisma/                # 数据库模型
├── docs/                  # 文档
└── scripts/               # 脚本文件
```

### 开发命令

```bash
# 开发模式
yarn dev

# 构建
yarn build

# 数据库操作
yarn db:generate    # 生成 Prisma 客户端
yarn db:migrate     # 运行迁移
yarn db:push        # 推送模型到数据库
yarn db:studio      # 打开数据库管理界面
yarn db:init        # 初始化数据

# 代码检查
yarn lint

# 运行测试
yarn test
```

## 📈 商业化优势

1. **降低使用门槛** - 用户无需了解 API 配置，开箱即用
2. **统一成本控制** - 管理员可以统一管理和控制 API 使用成本
3. **数据安全** - API Key 等敏感信息不会暴露给用户
4. **用量统计** - 完整的使用统计，便于成本分析和用户管理
5. **扩展性强** - 支持多租户、配额管理等企业级功能

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

本项目基于 [NextChat](https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web) 开发，感谢原作者的贡献。
